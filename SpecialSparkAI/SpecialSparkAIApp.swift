//
//  SpecialSparkAIApp.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

@main
struct SpecialSparkAIApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            // Student Models
            Student.self,
            // Subject is now a struct, not a SwiftData model
            StudentTeacherAssignment.self,
            LearningSession.self,

            // AI Agent Models
            AIAgent.self,
            AgentPersonality.self,
            AgentCapability.self,
            LearningInteraction.self,
        ])

        // Use in-memory store for now to avoid migration issues
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            print("Failed to create ModelContainer: \(error)")
            // Create a fallback in-memory container
            do {
                return try ModelContainer(for: schema, configurations: [ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)])
            } catch {
                fatalError("Could not create fallback ModelContainer: \(error)")
            }
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .modelContainer(sharedModelContainer)
    }
}
