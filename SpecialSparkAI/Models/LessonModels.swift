//
//  LessonModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Learning Resource Models

struct LearningResource: Identifiable, Codable {
    var id = UUID()
    let title: String
    let type: ResourceType
    let url: String?
    let description: String
    let estimatedTime: Int // in minutes

    enum ResourceType: String, CaseIterable, Codable {
        case video = "video"
        case article = "article"
        case interactive = "interactive"
        case worksheet = "worksheet"
        case game = "game"
        case simulation = "simulation"

        var displayName: String {
            switch self {
            case .video: return "Video"
            case .article: return "Article"
            case .interactive: return "Interactive"
            case .worksheet: return "Worksheet"
            case .game: return "Game"
            case .simulation: return "Simulation"
            }
        }

        var icon: String {
            switch self {
            case .video: return "play.rectangle"
            case .article: return "doc.text"
            case .interactive: return "hand.tap"
            case .worksheet: return "doc.on.clipboard"
            case .game: return "gamecontroller"
            case .simulation: return "cpu"
            }
        }
    }
}

// MARK: - Lesson System Models

struct Lesson: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let subjectId: UUID
    let gradeLevel: GradeLevel
    let schoolLevel: SchoolLevel
    let duration: Int // minutes
    let difficulty: DifficultyLevel
    let learningObjectives: [String]
    let prerequisites: [UUID]
    let topics: [LessonTopic]
    let assessments: [Assessment]
    let resources: [LearningResource]
    let adaptiveFeatures: [AdaptiveFeature]
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    init(title: String, description: String, subjectId: UUID, gradeLevel: GradeLevel, duration: Int = 45) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.subjectId = subjectId
        self.gradeLevel = gradeLevel
        self.schoolLevel = gradeLevel.schoolLevel
        self.duration = duration
        self.difficulty = .beginner
        self.learningObjectives = []
        self.prerequisites = []
        self.topics = []
        self.assessments = []
        self.resources = []
        self.adaptiveFeatures = []
        self.isActive = true
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

struct LessonTopic: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let content: TopicContent
    let examples: [Example]
    let exercises: [Exercise]
    let quizzes: [Quiz]
    let estimatedTime: Int // minutes
    let order: Int

    init(title: String, description: String, order: Int) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.content = TopicContent(text: "", multimedia: [])
        self.examples = []
        self.exercises = []
        self.quizzes = []
        self.estimatedTime = 15
        self.order = order
    }
}

struct TopicContent: Codable {
    let text: String
    let multimedia: [MultimediaResource]
    let interactiveElements: [InteractiveElement]

    init(text: String, multimedia: [MultimediaResource]) {
        self.text = text
        self.multimedia = multimedia
        self.interactiveElements = []
    }
}

struct MultimediaResource: Identifiable, Codable {
    let id: UUID
    let type: MediaType
    let url: String
    let title: String
    let description: String
    let duration: Int? // for videos/audio

    init(type: MediaType, url: String, title: String, description: String = "") {
        self.id = UUID()
        self.type = type
        self.url = url
        self.title = title
        self.description = description
        self.duration = nil
    }
}

enum MediaType: String, CaseIterable, Codable {
    case image = "image"
    case video = "video"
    case audio = "audio"
    case animation = "animation"
    case simulation = "simulation"
    case document = "document"
}

struct InteractiveElement: Identifiable, Codable {
    let id: UUID
    let type: LessonInteractionType
    let title: String
    let content: String
    let parameters: [String: String]

    init(type: LessonInteractionType, title: String, content: String) {
        self.id = UUID()
        self.type = type
        self.title = title
        self.content = content
        self.parameters = [:]
    }
}

enum LessonInteractionType: String, CaseIterable, Codable {
    case dragAndDrop = "drag_and_drop"
    case clickable = "clickable"
    case drawing = "drawing"
    case typing = "typing"
    case voiceInput = "voice_input"
    case gesture = "gesture"
}

struct Example: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let content: String
    let multimedia: [MultimediaResource]
    let stepByStep: [String]
    let difficulty: DifficultyLevel

    init(title: String, description: String, content: String) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.content = content
        self.multimedia = []
        self.stepByStep = []
        self.difficulty = .beginner
    }
}

struct Exercise: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: ExerciseType
    let content: String
    let solution: String
    let hints: [String]
    let difficulty: DifficultyLevel
    let points: Int
    let timeLimit: Int? // minutes

    init(title: String, description: String, type: ExerciseType, content: String, solution: String) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.type = type
        self.content = content
        self.solution = solution
        self.hints = []
        self.difficulty = .beginner
        self.points = 10
        self.timeLimit = nil
    }
}

enum ExerciseType: String, CaseIterable, Codable {
    case multipleChoice = "multiple_choice"
    case fillInBlank = "fill_in_blank"
    case shortAnswer = "short_answer"
    case essay = "essay"
    case matching = "matching"
    case ordering = "ordering"
    case calculation = "calculation"
    case coding = "coding"
    case drawing = "drawing"
    case experiment = "experiment"
}

struct Quiz: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let questions: [QuizQuestion]
    let timeLimit: Int // minutes
    let passingScore: Double // percentage
    let attempts: Int // allowed attempts
    let isAdaptive: Bool

    init(title: String, description: String, timeLimit: Int = 30) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.questions = []
        self.timeLimit = timeLimit
        self.passingScore = 70.0
        self.attempts = 3
        self.isAdaptive = true
    }
}

struct QuizQuestion: Identifiable, Codable {
    let id: UUID
    let question: String
    let type: LessonQuestionType
    let options: [String]
    let correctAnswer: String
    let explanation: String
    let points: Int
    let difficulty: DifficultyLevel

    init(question: String, type: LessonQuestionType, options: [String], correctAnswer: String, explanation: String = "") {
        self.id = UUID()
        self.question = question
        self.type = type
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.points = 1
        self.difficulty = .beginner
    }
}

enum LessonQuestionType: String, CaseIterable, Codable {
    case multipleChoice = "multiple_choice"
    case trueFalse = "true_false"
    case fillInBlank = "fill_in_blank"
    case shortAnswer = "short_answer"
    case matching = "matching"
    case ordering = "ordering"
}

// MARK: - Course Models

struct Course: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let subjectId: UUID
    let gradeLevel: GradeLevel
    let schoolLevel: SchoolLevel
    let lessons: [UUID] // lesson IDs
    let prerequisites: [UUID]
    let estimatedDuration: Int // total hours
    let difficulty: DifficultyLevel
    let category: CourseCategory
    let tags: [String]
    let isActive: Bool
    let createdAt: Date

    init(title: String, description: String, subjectId: UUID, gradeLevel: GradeLevel, category: CourseCategory) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.subjectId = subjectId
        self.gradeLevel = gradeLevel
        self.schoolLevel = gradeLevel.schoolLevel
        self.lessons = []
        self.prerequisites = []
        self.estimatedDuration = 0
        self.difficulty = .beginner
        self.category = category
        self.tags = []
        self.isActive = true
        self.createdAt = Date()
    }
}

enum CourseCategory: String, CaseIterable, Codable {
    case core = "core"
    case advanced = "advanced"
    case remedial = "remedial"
    case enrichment = "enrichment"
    case specialNeeds = "special_needs"
    case elective = "elective"
    case ap = "ap"
    case honors = "honors"
}
