//
//  AITeacherPersonality.swift
//  SpecialSparkAI
//
//  AI Teacher Personality System for Specialized Learning
//

import Foundation
import SwiftData

// MARK: - AI Teacher Personality Model
@Model
class AITeacherPersonality: Identifiable, Codable {
    @Attribute(.unique) var id: UUID
    var name: String
    var subject: String
    var traits: [String]
    var teachingStyle: String
    var gradeSpecialization: [GradeLevel]
    var avatarImageName: String
    var backgroundColor: String
    var systemPrompt: String
    var specialNeedsAdaptations: [String]
    var conversationStarters: [String]
    var encouragementPhrases: [String]
    var isActive: Bool
    var createdAt: Date

    init(
        name: String,
        subject: String,
        traits: [String],
        teachingStyle: String,
        gradeSpecialization: [GradeLevel],
        avatarImageName: String = "person.circle.fill",
        backgroundColor: String = "#007AFF",
        systemPrompt: String = "",
        specialNeedsAdaptations: [String] = [],
        conversationStarters: [String] = [],
        encouragementPhrases: [String] = []
    ) {
        self.id = UUID()
        self.name = name
        self.subject = subject
        self.traits = traits
        self.teachingStyle = teachingStyle
        self.gradeSpecialization = gradeSpecialization
        self.avatarImageName = avatarImageName
        self.backgroundColor = backgroundColor
        self.systemPrompt = systemPrompt.isEmpty ? Self.generateSystemPrompt(name: name, subject: subject, traits: traits, teachingStyle: teachingStyle) : systemPrompt
        self.specialNeedsAdaptations = specialNeedsAdaptations
        self.conversationStarters = conversationStarters.isEmpty ? Self.generateConversationStarters(subject: subject) : conversationStarters
        self.encouragementPhrases = encouragementPhrases.isEmpty ? Self.generateEncouragementPhrases() : encouragementPhrases
        self.isActive = true
        self.createdAt = Date()
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, name, subject, traits, teachingStyle, gradeSpecialization
        case avatarImageName, backgroundColor, systemPrompt, specialNeedsAdaptations
        case conversationStarters, encouragementPhrases, isActive, createdAt
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.subject = try container.decode(String.self, forKey: .subject)
        self.traits = try container.decode([String].self, forKey: .traits)
        self.teachingStyle = try container.decode(String.self, forKey: .teachingStyle)
        self.gradeSpecialization = try container.decode([GradeLevel].self, forKey: .gradeSpecialization)
        self.avatarImageName = try container.decode(String.self, forKey: .avatarImageName)
        self.backgroundColor = try container.decode(String.self, forKey: .backgroundColor)
        self.systemPrompt = try container.decode(String.self, forKey: .systemPrompt)
        self.specialNeedsAdaptations = try container.decode([String].self, forKey: .specialNeedsAdaptations)
        self.conversationStarters = try container.decode([String].self, forKey: .conversationStarters)
        self.encouragementPhrases = try container.decode([String].self, forKey: .encouragementPhrases)
        self.isActive = try container.decode(Bool.self, forKey: .isActive)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(subject, forKey: .subject)
        try container.encode(traits, forKey: .traits)
        try container.encode(teachingStyle, forKey: .teachingStyle)
        try container.encode(gradeSpecialization, forKey: .gradeSpecialization)
        try container.encode(avatarImageName, forKey: .avatarImageName)
        try container.encode(backgroundColor, forKey: .backgroundColor)
        try container.encode(systemPrompt, forKey: .systemPrompt)
        try container.encode(specialNeedsAdaptations, forKey: .specialNeedsAdaptations)
        try container.encode(conversationStarters, forKey: .conversationStarters)
        try container.encode(encouragementPhrases, forKey: .encouragementPhrases)
        try container.encode(isActive, forKey: .isActive)
        try container.encode(createdAt, forKey: .createdAt)
    }

    // MARK: - Static Factory Methods
    static func generateSystemPrompt(name: String, subject: String, traits: [String], teachingStyle: String) -> String {
        return """
        You are \(name), a specialized AI teacher for \(subject) with the following characteristics:

        PERSONALITY TRAITS: \(traits.joined(separator: ", "))
        TEACHING STYLE: \(teachingStyle)

        CORE PRINCIPLES:
        - Always be encouraging and patient
        - Adapt to each student's learning pace
        - Use age-appropriate language
        - Focus on building confidence
        - Celebrate small victories
        - Provide clear, step-by-step explanations

        SPECIAL NEEDS CONSIDERATIONS:
        - Be extra patient with processing time
        - Use simple, clear language
        - Break complex concepts into smaller parts
        - Provide multiple ways to understand concepts
        - Be sensitive to frustration and anxiety
        - Offer frequent positive reinforcement

        SAFETY GUIDELINES:
        - Keep all content educational and appropriate
        - Redirect inappropriate questions to learning topics
        - Never discuss personal information
        - Focus on building self-esteem and learning confidence

        Always respond in character as \(name) with these personality traits and teaching approach.
        """
    }

    static func generateConversationStarters(subject: String) -> [String] {
        switch subject.lowercased() {
        case "mathematics", "math":
            return [
                "What's your favorite number and why?",
                "Have you noticed any patterns around you today?",
                "Would you like to solve a fun math puzzle?",
                "Let's explore how math helps us in everyday life!",
                "What math topic would you like to discover today?"
            ]
        case "science":
            return [
                "What's something in nature that makes you curious?",
                "Would you like to do a virtual science experiment?",
                "What's the most amazing thing you've learned about space?",
                "Let's investigate how things work around us!",
                "What science question has been on your mind?"
            ]
        case "reading", "english", "language arts":
            return [
                "What's your favorite story or book?",
                "Would you like to create a story together?",
                "What character from a book would you like to meet?",
                "Let's explore the magic of words and stories!",
                "What kind of adventure should we read about today?"
            ]
        case "social studies", "history":
            return [
                "What time period would you like to visit?",
                "Let's explore different cultures around the world!",
                "What's something interesting about your community?",
                "Would you like to learn about amazing people from history?",
                "Let's discover how people lived long ago!"
            ]
        default:
            return [
                "What would you like to learn about today?",
                "I'm excited to explore new ideas with you!",
                "What questions do you have about the world?",
                "Let's discover something amazing together!",
                "What topic makes you most curious?"
            ]
        }
    }

    static func generateEncouragementPhrases() -> [String] {
        return [
            "You're doing amazing!",
            "I can see you're really thinking hard about this!",
            "That's a fantastic question!",
            "You're making great progress!",
            "I love how curious you are!",
            "You're such a thoughtful learner!",
            "That's exactly the right way to think about it!",
            "You should be proud of your effort!",
            "I can tell you're really understanding this!",
            "You're becoming quite the expert!",
            "Your questions show how smart you are!",
            "I'm so impressed with your thinking!",
            "You're doing better than you think!",
            "That's a brilliant observation!",
            "You're making this look easy!"
        ]
    }
}

// MARK: - Default Teacher Personalities
extension AITeacherPersonality {
    static func createDefaultTeachers() -> [AITeacherPersonality] {
        return [
            createMathTeacher(),
            createScienceTeacher(),
            createReadingTeacher(),
            createSpecialNeedsCoordinator()
        ]
    }

    static func createMathTeacher() -> AITeacherPersonality {
        return AITeacherPersonality(
            name: "Math Teacher",
            subject: "Mathematics",
            traits: ["Patient", "Encouraging", "Logical", "Creative", "Supportive"],
            teachingStyle: "Step-by-step problem solving with visual aids and real-world examples",
            gradeSpecialization: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
            avatarImageName: "person.crop.circle.fill",
            backgroundColor: "#FF6B6B",
            specialNeedsAdaptations: [
                "Extra processing time for calculations",
                "Visual number representations",
                "Breaking problems into smaller steps",
                "Using manipulatives and concrete examples",
                "Celebrating incremental progress"
            ]
        )
    }

    static func createScienceTeacher() -> AITeacherPersonality {
        return AITeacherPersonality(
            name: "Science Teacher",
            subject: "Science",
            traits: ["Curious", "Enthusiastic", "Experimental", "Wonder-filled", "Investigative"],
            teachingStyle: "Hands-on exploration with virtual experiments and discovery-based learning",
            gradeSpecialization: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5, .grade6],
            avatarImageName: "person.crop.circle.fill",
            backgroundColor: "#4ECDC4",
            specialNeedsAdaptations: [
                "Multi-sensory science experiences",
                "Simple cause-and-effect demonstrations",
                "Repeated observations and patterns",
                "Safe virtual experimentation",
                "Wonder-based questioning"
            ]
        )
    }

    static func createReadingTeacher() -> AITeacherPersonality {
        return AITeacherPersonality(
            name: "Reading Teacher",
            subject: "Reading & Language Arts",
            traits: ["Creative", "Storytelling", "Imaginative", "Expressive", "Nurturing"],
            teachingStyle: "Story-based learning with character development and creative expression",
            gradeSpecialization: [.kindergarten, .grade1, .grade2, .grade3, .grade4],
            avatarImageName: "person.crop.circle.fill",
            backgroundColor: "#A8E6CF",
            specialNeedsAdaptations: [
                "Picture-supported reading",
                "Phonetic breakdown assistance",
                "Emotional connection to stories",
                "Multiple reading modalities",
                "Confidence building through success"
            ]
        )
    }

    static func createSpecialNeedsCoordinator() -> AITeacherPersonality {
        return AITeacherPersonality(
            name: "Special Needs Coordinator",
            subject: "Special Needs Support",
            traits: ["Adaptive", "Understanding", "Patient", "Flexible", "Empathetic"],
            teachingStyle: "Individualized support with sensory considerations and emotional intelligence",
            gradeSpecialization: GradeLevel.allCases,
            avatarImageName: "person.crop.circle.fill",
            backgroundColor: "#FFD93D",
            specialNeedsAdaptations: [
                "Sensory-friendly interactions",
                "Emotional regulation support",
                "Flexible pacing and breaks",
                "Multiple communication methods",
                "Strength-based learning approaches",
                "Anxiety and stress management",
                "Social skills development"
            ]
        )
    }
}

// MARK: - Personality Extensions
extension AITeacherPersonality {
    var displayColor: String {
        return backgroundColor
    }

    var gradeRange: String {
        let grades = gradeSpecialization.map { $0.displayName }
        if grades.count <= 2 {
            return grades.joined(separator: ", ")
        } else {
            return "\(grades.first ?? "") - \(grades.last ?? "")"
        }
    }

    func getRandomConversationStarter() -> String {
        return conversationStarters.randomElement() ?? "What would you like to learn about today?"
    }

    func getRandomEncouragement() -> String {
        return encouragementPhrases.randomElement() ?? "You're doing great!"
    }

    func isSpecializedFor(grade: GradeLevel) -> Bool {
        return gradeSpecialization.contains(grade)
    }

    func getPersonalizedSystemPrompt(for student: Student) -> String {
        var personalizedPrompt = systemPrompt

        personalizedPrompt += """

        STUDENT PROFILE:
        - Name: \(student.firstName)
        - Grade Level: \(student.gradeLevel.displayName)
        - Special Needs: \(student.specialNeeds.map { $0.rawValue }.joined(separator: ", "))

        Adapt your teaching approach specifically for this student's needs and grade level.
        """

        return personalizedPrompt
    }
}
