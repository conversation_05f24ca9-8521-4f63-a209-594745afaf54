//
//  SupabaseModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation

// MARK: - Supabase Database Models

// MARK: - Student Profile
struct StudentProfile: Codable, Identifiable {
    let id: UUID
    let firstName: String
    let lastName: String
    let dateOfBirth: String
    let gradeLevel: String
    let schoolLevel: String
    let profileImageURL: String?
    let parentEmail: String?
    let specialNeeds: [String]
    let learningStyle: String
    let preferredLanguage: String
    let timezone: String
    let isActive: Bool
    let createdAt: String
    let updatedAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case firstName = "first_name"
        case lastName = "last_name"
        case dateOfBirth = "date_of_birth"
        case gradeLevel = "grade_level"
        case schoolLevel = "school_level"
        case profileImageURL = "profile_image_url"
        case parentEmail = "parent_email"
        case specialNeeds = "special_needs"
        case learningStyle = "learning_style"
        case preferredLanguage = "preferred_language"
        case timezone
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Learning Session
struct LearningSessionRecord: Codable, Identifiable {
    let id: UUID
    let studentId: UUID
    let subjectId: UUID
    let teacherAgentId: UUID
    let sessionType: String
    let startTime: String
    let endTime: String?
    let duration: Int?
    let topicsCovered: [String]
    let difficultyLevel: String
    let completionRate: Double
    let engagementScore: Double
    let emotionalState: String
    let adaptationsUsed: [String]
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case studentId = "student_id"
        case subjectId = "subject_id"
        case teacherAgentId = "teacher_agent_id"
        case sessionType = "session_type"
        case startTime = "start_time"
        case endTime = "end_time"
        case duration
        case topicsCovered = "topics_covered"
        case difficultyLevel = "difficulty_level"
        case completionRate = "completion_rate"
        case engagementScore = "engagement_score"
        case emotionalState = "emotional_state"
        case adaptationsUsed = "adaptations_used"
        case createdAt = "created_at"
    }
}

// MARK: - Learning Progress
struct LearningProgress: Codable, Identifiable {
    let id: UUID
    let studentId: UUID
    let subjectId: UUID
    let skillId: UUID
    let currentLevel: String
    let masteryScore: Double
    let attemptsCount: Int
    let lastAttemptDate: String
    let strengthAreas: [String]
    let improvementAreas: [String]
    let nextRecommendations: [String]
    let adaptivePath: String
    let createdAt: String
    let updatedAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case studentId = "student_id"
        case subjectId = "subject_id"
        case skillId = "skill_id"
        case currentLevel = "current_level"
        case masteryScore = "mastery_score"
        case attemptsCount = "attempts_count"
        case lastAttemptDate = "last_attempt_date"
        case strengthAreas = "strength_areas"
        case improvementAreas = "improvement_areas"
        case nextRecommendations = "next_recommendations"
        case adaptivePath = "adaptive_path"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Assessment Result
struct AssessmentRecord: Codable, Identifiable {
    let id: UUID
    let studentId: UUID
    let sessionId: UUID
    let questionId: UUID
    let questionText: String
    let studentAnswer: String
    let correctAnswer: String
    let isCorrect: Bool
    let responseTime: Double
    let hintsUsed: Int
    let difficultyLevel: String
    let skillTested: String
    let adaptiveAdjustment: String?
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case studentId = "student_id"
        case sessionId = "session_id"
        case questionId = "question_id"
        case questionText = "question_text"
        case studentAnswer = "student_answer"
        case correctAnswer = "correct_answer"
        case isCorrect = "is_correct"
        case responseTime = "response_time"
        case hintsUsed = "hints_used"
        case difficultyLevel = "difficulty_level"
        case skillTested = "skill_tested"
        case adaptiveAdjustment = "adaptive_adjustment"
        case createdAt = "created_at"
    }
}

// MARK: - AI Teacher Agent
struct AITeacherRecord: Codable, Identifiable {
    let id: UUID
    let name: String
    let role: String
    let specialization: String
    let gradeLevel: String
    let personality: String
    let avatarURL: String?
    let capabilities: [String]
    let adaptationStrategies: [String]
    let isActive: Bool
    let createdAt: String
    let updatedAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case role
        case specialization
        case gradeLevel = "grade_level"
        case personality
        case avatarURL = "avatar_url"
        case capabilities
        case adaptationStrategies = "adaptation_strategies"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Subject
struct SubjectRecord: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let gradeLevel: String
    let schoolLevel: String
    let skills: [String]
    let prerequisites: [String]
    let learningObjectives: [String]
    let isActive: Bool
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case gradeLevel = "grade_level"
        case schoolLevel = "school_level"
        case skills
        case prerequisites
        case learningObjectives = "learning_objectives"
        case isActive = "is_active"
        case createdAt = "created_at"
    }
}

// MARK: - Adaptive Learning Recommendation
struct AdaptiveLearningRecommendation: Codable, Identifiable {
    let id: UUID
    let studentId: UUID
    let recommendationType: String
    let priority: String
    let title: String
    let description: String
    let actionItems: [String]
    let estimatedDuration: Int
    let difficultyAdjustment: String?
    let personalityAdjustment: String?
    let isCompleted: Bool
    let createdAt: String
    let completedAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case studentId = "student_id"
        case recommendationType = "recommendation_type"
        case priority
        case title
        case description
        case actionItems = "action_items"
        case estimatedDuration = "estimated_duration"
        case difficultyAdjustment = "difficulty_adjustment"
        case personalityAdjustment = "personality_adjustment"
        case isCompleted = "is_completed"
        case createdAt = "created_at"
        case completedAt = "completed_at"
    }
}

// MARK: - Emotional State Tracking
struct EmotionalStateRecord: Codable, Identifiable {
    let id: UUID
    let studentId: UUID
    let sessionId: UUID?
    let emotionalState: String
    let confidence: Double
    let engagement: Double
    let frustration: Double
    let excitement: Double
    let context: String
    let triggers: [String]
    let interventions: [String]
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case studentId = "student_id"
        case sessionId = "session_id"
        case emotionalState = "emotional_state"
        case confidence
        case engagement
        case frustration
        case excitement
        case context
        case triggers
        case interventions
        case createdAt = "created_at"
    }
}

// MARK: - Parent Dashboard Data
struct ParentDashboardRecord: Codable {
    let studentId: UUID
    let weeklyProgress: [String: Double]
    let subjectPerformance: [String: Double]
    let recentAchievements: [String]
    let upcomingGoals: [String]
    let recommendedActivities: [String]
    let timeSpentLearning: Int
    let engagementTrends: [String: Double]
    let lastUpdated: String

    enum CodingKeys: String, CodingKey {
        case studentId = "student_id"
        case weeklyProgress = "weekly_progress"
        case subjectPerformance = "subject_performance"
        case recentAchievements = "recent_achievements"
        case upcomingGoals = "upcoming_goals"
        case recommendedActivities = "recommended_activities"
        case timeSpentLearning = "time_spent_learning"
        case engagementTrends = "engagement_trends"
        case lastUpdated = "last_updated"
    }
}
