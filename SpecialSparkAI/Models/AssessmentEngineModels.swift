//
//  AssessmentEngineModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Assessment Engine Models

struct Assessment: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: AssessmentType
    let subject: String
    let gradeLevel: String
    let difficulty: DifficultyLevel
    let estimatedDuration: Int // minutes
    let questions: [AssessmentQuestion]
    let rubrics: [AssessmentRubric]
    let adaptiveSettings: AdaptiveAssessmentSettings
    let accommodations: [AssessmentAccommodation]
    let scoringCriteria: ScoringCriteria
    let isActive: Bool
    let createdAt: String
    let updatedAt: String
}

enum AssessmentType: String, CaseIterable, Codable {
    case diagnostic = "Diagnostic"
    case formative = "Formative"
    case summative = "Summative"
    case benchmark = "Benchmark"
    case placement = "Placement"
    case progress = "Progress Monitoring"
    case competency = "Competency-Based"
    case portfolio = "Portfolio"
    case performance = "Performance Task"
    case authentic = "Authentic Assessment"
}

struct AssessmentQuestion: Identifiable, Codable {
    let id: UUID
    let questionText: String
    let questionType: QuestionType
    let options: [QuestionOption]?
    let correctAnswers: [String]
    let points: Int
    let difficulty: QuestionDifficulty
    let bloomsLevel: BloomsLevel
    let learningObjective: String
    let hints: [Hint]
    let multimedia: [MultimediaElement]
    let adaptiveRules: [AdaptiveRule]
    let timeLimit: Int? // seconds
    let allowPartialCredit: Bool
}

enum QuestionType: String, CaseIterable, Codable {
    case multipleChoice = "Multiple Choice"
    case trueFalse = "True/False"
    case shortAnswer = "Short Answer"
    case essay = "Essay"
    case fillInBlank = "Fill in the Blank"
    case matching = "Matching"
    case ordering = "Ordering"
    case dragDrop = "Drag and Drop"
    case hotspot = "Hotspot"
    case drawing = "Drawing"
    case recording = "Audio/Video Recording"
    case simulation = "Simulation"
}

struct QuestionOption: Identifiable, Codable {
    let id: UUID
    let text: String
    let isCorrect: Bool
    let feedback: String?
    let multimedia: MultimediaElement?
}

enum QuestionDifficulty: String, CaseIterable, Codable {
    case veryEasy = "Very Easy"
    case easy = "Easy"
    case medium = "Medium"
    case hard = "Hard"
    case veryHard = "Very Hard"

    var points: Int {
        switch self {
        case .veryEasy: return 1
        case .easy: return 2
        case .medium: return 3
        case .hard: return 4
        case .veryHard: return 5
        }
    }
}

struct Hint: Identifiable, Codable {
    let id: UUID
    let text: String
    let level: HintLevel
    let pointDeduction: Int
    let multimedia: MultimediaElement?
}

enum HintLevel: String, CaseIterable, Codable {
    case gentle = "Gentle"
    case moderate = "Moderate"
    case strong = "Strong"
    case solution = "Solution"
}

struct MultimediaElement: Identifiable, Codable {
    let id: UUID
    let type: MultimediaType
    let url: String
    let altText: String?
    let caption: String?
    let duration: Int? // for audio/video
}

enum MultimediaType: String, CaseIterable, Codable {
    case image = "Image"
    case audio = "Audio"
    case video = "Video"
    case animation = "Animation"
    case interactive = "Interactive"
}

struct AdaptiveRule: Identifiable, Codable {
    let id: UUID
    let condition: String
    let action: AdaptiveAction
    let parameters: [String: String]
}

enum AdaptiveAction: String, CaseIterable, Codable {
    case skipQuestion = "Skip Question"
    case provideHint = "Provide Hint"
    case adjustDifficulty = "Adjust Difficulty"
    case offerBreak = "Offer Break"
    case changePresentation = "Change Presentation"
    case provideFeedback = "Provide Feedback"
}

struct AssessmentRubric: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let criteria: [RubricCriterion]
    let scoringScale: ScoringScale
    let isHolistic: Bool
}

struct RubricCriterion: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let weight: Double
    let performanceLevels: [PerformanceLevel]
}

struct PerformanceLevel: Identifiable, Codable {
    let id: UUID
    let level: Int
    let name: String
    let description: String
    let points: Int
    let exemplars: [String]
}

enum ScoringScale: String, CaseIterable, Codable {
    case fourPoint = "4-Point"
    case fivePoint = "5-Point"
    case sixPoint = "6-Point"
    case percentage = "Percentage"
    case passFail = "Pass/Fail"
    case custom = "Custom"
}

struct AdaptiveAssessmentSettings: Codable {
    let isAdaptive: Bool
    let adaptationAlgorithm: AdaptationAlgorithm
    let minimumQuestions: Int
    let maximumQuestions: Int
    let terminationCriteria: [TerminationCriterion]
    let difficultyAdjustment: DifficultyAdjustment
    let contentBalancing: ContentBalancing
}

enum AdaptationAlgorithm: String, CaseIterable, Codable {
    case irt = "Item Response Theory"
    case cat = "Computer Adaptive Testing"
    case bayesian = "Bayesian"
    case rulesBased = "Rules-Based"
    case machineLearning = "Machine Learning"
}

struct TerminationCriterion: Identifiable, Codable {
    let id: UUID
    let type: TerminationType
    let threshold: Double
    let priority: Int
}

enum TerminationType: String, CaseIterable, Codable {
    case standardError = "Standard Error"
    case confidence = "Confidence Level"
    case timeLimit = "Time Limit"
    case questionLimit = "Question Limit"
    case mastery = "Mastery Level"
}

struct DifficultyAdjustment: Codable {
    let initialDifficulty: Double
    let adjustmentRate: Double
    let minimumDifficulty: Double
    let maximumDifficulty: Double
    let smoothingFactor: Double
}

struct ContentBalancing: Codable {
    let enforceBalance: Bool
    let contentAreas: [ContentArea]
    let balancingStrategy: BalancingStrategy
}

struct ContentArea: Identifiable, Codable {
    let id: UUID
    let name: String
    let weight: Double
    let minimumQuestions: Int
    let maximumQuestions: Int
}

enum BalancingStrategy: String, CaseIterable, Codable {
    case proportional = "Proportional"
    case equal = "Equal"
    case weighted = "Weighted"
    case adaptive = "Adaptive"
}

struct AssessmentAccommodation: Identifiable, Codable {
    let id: UUID
    let type: AccommodationType
    let description: String
    let implementation: AccommodationImplementation
    let isRequired: Bool
    let studentIds: [UUID]
}

struct AccommodationImplementation: Codable {
    let settings: [String: String]
    let instructions: String
    let verification: String
}

struct ScoringCriteria: Codable {
    let totalPoints: Int
    let passingScore: Double
    let gradingScale: GradingScale
    let weightedScoring: Bool
    let partialCreditRules: [PartialCreditRule]
    let bonusPoints: [BonusPoint]
}

struct GradingScale: Codable {
    let type: GradingType
    let ranges: [GradeRange]
}

enum GradingType: String, CaseIterable, Codable {
    case letter = "Letter Grade"
    case percentage = "Percentage"
    case points = "Points"
    case passFail = "Pass/Fail"
    case proficiency = "Proficiency Level"
}

struct GradeRange: Identifiable, Codable {
    let id: UUID
    let grade: String
    let minimumScore: Double
    let maximumScore: Double
    let description: String
}

struct PartialCreditRule: Identifiable, Codable {
    let id: UUID
    let questionType: QuestionType
    let rule: String
    let creditPercentage: Double
}

struct BonusPoint: Identifiable, Codable {
    let id: UUID
    let condition: String
    let points: Int
    let description: String
}

// MARK: - Assessment Session Models

struct AssessmentSession: Identifiable, Codable {
    let id: UUID
    let assessmentId: UUID
    let studentId: UUID
    let startTime: String
    let endTime: String?
    let status: SessionStatus
    let responses: [AssessmentResponse]
    let score: AssessmentScore?
    let adaptiveData: AdaptiveSessionData
    let accommodationsUsed: [UUID]
    let technicalIssues: [TechnicalIssue]
    let proctorNotes: [ProctorNote]
}

enum SessionStatus: String, CaseIterable, Codable {
    case notStarted = "Not Started"
    case inProgress = "In Progress"
    case paused = "Paused"
    case completed = "Completed"
    case abandoned = "Abandoned"
    case invalidated = "Invalidated"
}

struct AssessmentResponse: Identifiable, Codable {
    let id: UUID
    let questionId: UUID
    let response: String
    let isCorrect: Bool?
    let pointsEarned: Int
    let timeSpent: Int // seconds
    let hintsUsed: [UUID]
    let attempts: Int
    let confidence: Double?
    let responseMetadata: ResponseMetadata
}

struct ResponseMetadata: Codable {
    let keystrokeData: [Keystroke]?
    let mouseMovements: [MouseMovement]?
    let eyeTracking: [EyeGaze]?
    let audioLevel: Double?
    let screenRecording: String?
}

struct Keystroke: Codable {
    let timestamp: String
    let key: String
    let action: KeyAction
}

enum KeyAction: String, CaseIterable, Codable {
    case press = "Press"
    case release = "Release"
}

struct MouseMovement: Codable {
    let timestamp: String
    let x: Double
    let y: Double
    let action: MouseAction
}

enum MouseAction: String, CaseIterable, Codable {
    case move = "Move"
    case click = "Click"
    case scroll = "Scroll"
    case drag = "Drag"
}

struct EyeGaze: Codable {
    let timestamp: String
    let x: Double
    let y: Double
    let duration: Int // milliseconds
}

struct AssessmentScore: Codable {
    let totalPoints: Int
    let earnedPoints: Int
    let percentage: Double
    let grade: String
    let proficiencyLevel: String
    let subscores: [Subscore]
    let standardError: Double?
    let confidenceInterval: ConfidenceInterval?
}

struct Subscore: Identifiable, Codable {
    let id: UUID
    let category: String
    let points: Int
    let maxPoints: Int
    let percentage: Double
}

struct ConfidenceInterval: Codable {
    let lower: Double
    let upper: Double
    let confidence: Double
}

struct AdaptiveSessionData: Codable {
    let abilityEstimate: Double?
    let standardError: Double?
    let questionsAdministered: Int
    let adaptationHistory: [AdaptationEvent]
    let terminationReason: String?
}

struct AdaptationEvent: Identifiable, Codable {
    let id: UUID
    let timestamp: String
    let event: String
    let parameters: [String: String]
    let result: String
}

struct TechnicalIssue: Identifiable, Codable {
    let id: UUID
    let timestamp: String
    let type: IssueType
    let description: String
    let severity: IssueSeverity
    let resolution: String?
}

enum IssueType: String, CaseIterable, Codable {
    case connectivity = "Connectivity"
    case browser = "Browser"
    case audio = "Audio"
    case video = "Video"
    case input = "Input Device"
    case display = "Display"
    case performance = "Performance"
}

enum IssueSeverity: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"
}

struct ProctorNote: Identifiable, Codable {
    let id: UUID
    let timestamp: String
    let note: String
    let category: NoteCategory
    let flagged: Bool
}

// NoteCategory is defined in GameModels.swift to avoid conflicts

// MARK: - Assessment Analytics

struct AssessmentAnalytics: Codable {
    let assessmentId: UUID
    let totalSessions: Int
    let completionRate: Double
    let averageScore: Double
    let averageDuration: Int
    let difficultyAnalysis: DifficultyAnalysis
    let itemAnalysis: [ItemAnalysis]
    let performanceDistribution: PerformanceDistribution
    let reliabilityMetrics: ReliabilityMetrics
    let validityIndicators: ValidityIndicators
}

struct DifficultyAnalysis: Codable {
    let averageDifficulty: Double
    let difficultyRange: ClosedRange<Double>
    let optimalDifficulty: Double
    let recommendations: [String]
}

struct ItemAnalysis: Identifiable, Codable {
    let id: UUID
    let questionId: UUID
    let difficulty: Double
    let discrimination: Double
    let pointBiserial: Double
    let distractorAnalysis: [DistractorAnalysis]
    let flagged: Bool
    let recommendations: [String]
}

struct DistractorAnalysis: Identifiable, Codable {
    let id: UUID
    let option: String
    let selectionRate: Double
    let pointBiserial: Double
    let effectiveness: Double
}

struct PerformanceDistribution: Codable {
    let mean: Double
    let median: Double
    let mode: Double
    let standardDeviation: Double
    let skewness: Double
    let kurtosis: Double
    let percentiles: [Percentile]
}

struct Percentile: Identifiable, Codable {
    let id: UUID
    let percentile: Int
    let score: Double
}

struct ReliabilityMetrics: Codable {
    let cronbachAlpha: Double
    let splitHalf: Double
    let testRetest: Double?
    let standardError: Double
}

struct ValidityIndicators: Codable {
    let contentValidity: Double
    let constructValidity: Double
    let criterionValidity: Double?
    let faceValidity: Double
}
