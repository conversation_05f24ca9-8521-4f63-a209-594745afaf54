//
//  EnhancedSubjectModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Enhanced Subject System

struct EnhancedSubject: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let icon: String
    let color: String
    let category: SubjectCategory
    let gradeLevel: String
    let difficulty: DifficultyLevel
    let prerequisites: [UUID]
    let learningObjectives: [LearningObjective]
    let assessmentCriteria: [AssessmentCriterion]
    let adaptiveFeatures: [AdaptiveFeature]
    let specialNeedsSupport: [SpecialNeedsSupport]
    let estimatedDuration: Int // minutes
    let isActive: Bool
    let createdAt: String
    let updatedAt: String
}

// SubjectCategory is defined in StudentModels.swift to avoid conflicts



struct LearningObjective: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let bloomsLevel: BloomsLevel
    let assessmentMethod: AssessmentMethod
    let weight: Double // 0.0 to 1.0
    let isRequired: Bool
}

enum BloomsLevel: String, CaseIterable, Codable {
    case remember = "Remember"
    case understand = "Understand"
    case apply = "Apply"
    case analyze = "Analyze"
    case evaluate = "Evaluate"
    case create = "Create"

    var color: Color {
        switch self {
        case .remember: return .gray
        case .understand: return .blue
        case .apply: return .green
        case .analyze: return .orange
        case .evaluate: return .red
        case .create: return .purple
        }
    }
}

enum AssessmentMethod: String, CaseIterable, Codable {
    case quiz = "Quiz"
    case project = "Project"
    case presentation = "Presentation"
    case portfolio = "Portfolio"
    case observation = "Observation"
    case selfAssessment = "Self Assessment"
    case peerAssessment = "Peer Assessment"
    case performance = "Performance Task"
}

struct AssessmentCriterion: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let rubricLevels: [RubricLevel]
    let weight: Double
    let isRequired: Bool
}

struct RubricLevel: Identifiable, Codable {
    let id: UUID
    let level: Int // 1-4
    let name: String
    let description: String
    let points: Int
}

struct AdaptiveFeature: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let type: AdaptiveType
    let parameters: [String: String]
    let isEnabled: Bool
}

enum AdaptiveType: String, CaseIterable, Codable {
    case difficultyAdjustment = "Difficulty Adjustment"
    case pacingControl = "Pacing Control"
    case contentPersonalization = "Content Personalization"
    case multimodalPresentation = "Multimodal Presentation"
    case scaffolding = "Scaffolding"
    case reinforcement = "Reinforcement"
    case remediation = "Remediation"
    case enrichment = "Enrichment"
}

struct SpecialNeedsSupport: Identifiable, Codable {
    let id: UUID
    let needType: SpecialNeedType
    let accommodations: [Accommodation]
    let modifications: [Modification]
    let assistiveTechnology: [AssistiveTechnology]
    let isRequired: Bool
}

enum SpecialNeedType: String, CaseIterable, Codable {
    case autism = "Autism"
    case adhd = "ADHD"
    case dyslexia = "Dyslexia"
    case visualImpairment = "Visual Impairment"
    case hearingImpairment = "Hearing Impairment"
    case motorImpairment = "Motor Impairment"
    case intellectualDisability = "Intellectual Disability"
    case emotionalDisturbance = "Emotional Disturbance"
    case speechLanguage = "Speech/Language"
    case multipleDisabilities = "Multiple Disabilities"
}

struct Accommodation: Identifiable, Codable {
    let id: UUID
    let type: AccommodationType
    let description: String
    let implementation: String
    let isActive: Bool
}

enum AccommodationType: String, CaseIterable, Codable {
    case timing = "Timing"
    case setting = "Setting"
    case presentation = "Presentation"
    case response = "Response"
    case scheduling = "Scheduling"
}

struct Modification: Identifiable, Codable {
    let id: UUID
    let type: ModificationType
    let description: String
    let implementation: String
    let isActive: Bool
}

enum ModificationType: String, CaseIterable, Codable {
    case content = "Content"
    case process = "Process"
    case product = "Product"
    case environment = "Environment"
}

struct AssistiveTechnology: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: AssistiveTechType
    let description: String
    let configuration: [String: String]
    let isAvailable: Bool
}

enum AssistiveTechType: String, CaseIterable, Codable {
    case textToSpeech = "Text-to-Speech"
    case speechToText = "Speech-to-Text"
    case screenReader = "Screen Reader"
    case magnification = "Magnification"
    case alternativeKeyboard = "Alternative Keyboard"
    case eyeTracking = "Eye Tracking"
    case switchAccess = "Switch Access"
    case communicationDevice = "Communication Device"
}

// MARK: - Subject Progression System

struct SubjectProgression: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let subjectId: UUID
    let currentLevel: DifficultyLevel
    let completedObjectives: [UUID]
    let masteryScores: [UUID: Double]
    let timeSpent: Int // minutes
    let lastAccessed: String
    let nextRecommendations: [UUID]
    let strugglingAreas: [UUID]
    let strengthAreas: [UUID]
    let adaptiveAdjustments: [AdaptiveAdjustment]
}

struct AdaptiveAdjustment: Identifiable, Codable {
    let id: UUID
    let type: AdaptiveType
    let reason: String
    let adjustment: String
    let appliedAt: String
    let effectiveness: Double?
}

// MARK: - Curriculum Mapping

struct CurriculumStandard: Identifiable, Codable {
    let id: UUID
    let standardId: String
    let title: String
    let description: String
    let gradeLevel: String
    let subject: String
    let domain: String
    let cluster: String
    let standard: String
    let alignedObjectives: [UUID]
}

struct LearningPathway: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let subjects: [UUID]
    let prerequisites: [UUID]
    let estimatedDuration: Int
    let targetGrade: String
    let difficulty: DifficultyLevel
    let specializations: [Specialization]
}

struct Specialization: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let focusAreas: [String]
    let careerConnections: [String]
    let additionalResources: [String]
}

// MARK: - Enhanced Subject Service Protocol

protocol EnhancedSubjectServiceProtocol {
    func getAllSubjects() async -> [EnhancedSubject]
    func getSubjectsByCategory(_ category: SubjectCategory) async -> [EnhancedSubject]
    func getSubjectsByGrade(_ grade: String) async -> [EnhancedSubject]
    func getAdaptiveRecommendations(for studentId: UUID) async -> [EnhancedSubject]
    func updateSubjectProgression(_ progression: SubjectProgression) async
    func getSpecialNeedsSupport(for needTypes: [SpecialNeedType]) async -> [SpecialNeedsSupport]
    func generateLearningPathway(for studentId: UUID, goals: [String]) async -> LearningPathway
}
