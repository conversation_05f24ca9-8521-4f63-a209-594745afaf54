//
//  ParentPortalModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Parent Portal Models

struct ParentProfile: Identifiable, Codable {
    let id: UUID
    let firstName: String
    let lastName: String
    let email: String
    let phoneNumber: String?
    let relationship: ParentRelationship
    let children: [UUID] // Student IDs
    let preferences: ParentPreferences
    let communicationSettings: CommunicationSettings
    let accessLevel: AccessLevel
    let isActive: Bool
    let createdAt: String
    let lastLogin: String?
}

enum ParentRelationship: String, CaseIterable, Codable {
    case mother = "Mother"
    case father = "Father"
    case guardian = "Guardian"
    case stepParent = "Step-parent"
    case grandparent = "Grandparent"
    case caregiver = "Caregiver"
    case other = "Other"
}

struct ParentPreferences: Codable {
    let language: String
    let timezone: String
    let reportFrequency: ReportFrequency
    let notificationTypes: [NotificationType]
    let dashboardLayout: DashboardLayout
    let dataVisualization: DataVisualizationPreference
}

enum ReportFrequency: String, CaseIterable, Codable {
    case daily = "Daily"
    case weekly = "Weekly"
    case biweekly = "Bi-weekly"
    case monthly = "Monthly"
    case asNeeded = "As Needed"
}

enum NotificationType: String, CaseIterable, Codable {
    case achievements = "Achievements"
    case concerns = "Concerns"
    case progress = "Progress Updates"
    case assignments = "Assignments"
    case behavior = "Behavior Reports"
    case attendance = "Attendance"
    case meetings = "Meeting Reminders"
    case system = "System Updates"
}

struct CommunicationSettings: Codable {
    let preferredMethod: CommunicationMethod
    let availableHours: AvailabilityWindow
    let emergencyContact: Bool
    let autoResponders: [AutoResponder]
    let messageFilters: [MessageFilter]
}

enum CommunicationMethod: String, CaseIterable, Codable {
    case email = "Email"
    case sms = "SMS"
    case app = "In-App"
    case phone = "Phone"
    case video = "Video Call"
}

struct AvailabilityWindow: Codable {
    let startTime: String
    let endTime: String
    let timezone: String
    let weekdays: [Int] // 0-6, Sunday = 0
}

struct AutoResponder: Identifiable, Codable {
    let id: UUID
    let trigger: String
    let response: String
    let isActive: Bool
}

struct MessageFilter: Identifiable, Codable {
    let id: UUID
    let type: FilterType
    let criteria: String
    let action: FilterAction
}

enum FilterType: String, CaseIterable, Codable {
    case keyword = "Keyword"
    case sender = "Sender"
    case priority = "Priority"
    case category = "Category"
}

enum FilterAction: String, CaseIterable, Codable {
    case highlight = "Highlight"
    case categorize = "Categorize"
    case forward = "Forward"
    case archive = "Archive"
}

enum AccessLevel: String, CaseIterable, Codable {
    case full = "Full Access"
    case limited = "Limited Access"
    case view = "View Only"
    case emergency = "Emergency Only"
}

enum DashboardLayout: String, CaseIterable, Codable {
    case overview = "Overview"
    case detailed = "Detailed"
    case simplified = "Simplified"
    case custom = "Custom"
}

struct DataVisualizationPreference: Codable {
    let chartTypes: [ChartType]
    let colorScheme: ColorScheme
    let animationsEnabled: Bool
    let detailLevel: DetailLevel
}

enum ChartType: String, CaseIterable, Codable {
    case line = "Line Chart"
    case bar = "Bar Chart"
    case pie = "Pie Chart"
    case progress = "Progress Bar"
    case heatmap = "Heat Map"
}

enum ColorScheme: String, CaseIterable, Codable {
    case standard = "Standard"
    case highContrast = "High Contrast"
    case colorBlind = "Color Blind Friendly"
    case monochrome = "Monochrome"
}

enum DetailLevel: String, CaseIterable, Codable {
    case high = "High Detail"
    case medium = "Medium Detail"
    case low = "Low Detail"
    case summary = "Summary Only"
}

// MARK: - Parent Dashboard Data

struct ParentDashboard: Codable {
    let parentId: UUID
    let children: [ChildSummary]
    let recentActivity: [ActivitySummary]
    let upcomingEvents: [UpcomingEvent]
    let alerts: [ParentAlert]
    let progressOverview: ProgressOverview
    let communicationSummary: CommunicationSummary
    let recommendations: [ParentRecommendation]
    let lastUpdated: String

    // Additional properties for Phase 3
    var weeklyProgress: WeeklyProgress?
    var recentAlerts: [ParentAlert] = []
    var notificationSettings: ParentNotificationSettings?
    var privacySettings: ParentPrivacySettings?
}

struct ChildSummary: Identifiable, Codable {
    let id: UUID
    let name: String
    let grade: String
    let overallProgress: Double
    let currentMood: String
    let todaysActivities: Int
    let weeklyGoalProgress: Double
    let recentAchievements: [String]
    let areasOfConcern: [String]
    let nextSession: String?
}

struct ActivitySummary: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let studentName: String
    let activity: String
    let subject: String
    let duration: Int
    let performance: Double
    let timestamp: String
    let highlights: [String]
}

struct UpcomingEvent: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: EventType
    let scheduledTime: String
    let participants: [String]
    let isRequired: Bool
    let reminderSet: Bool
}

enum EventType: String, CaseIterable, Codable {
    case assessment = "Assessment"
    case meeting = "Meeting"
    case presentation = "Presentation"
    case fieldTrip = "Field Trip"
    case workshop = "Workshop"
    case conference = "Conference"
}

struct ParentAlert: Identifiable, Codable {
    let id: UUID
    let type: AlertType
    let priority: AlertPriority
    let title: String
    let message: String
    let studentId: UUID
    let actionRequired: Bool
    let possibleActions: [AlertAction]
    let timestamp: String
    let isRead: Bool
}

enum AlertType: String, CaseIterable, Codable {
    case achievement = "Achievement"
    case concern = "Concern"
    case behavior = "Behavior"
    case academic = "Academic"
    case health = "Health"
    case attendance = "Attendance"
    case system = "System"
}

enum AlertPriority: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case urgent = "Urgent"

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .urgent: return .red
        }
    }
}

struct AlertAction: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: ActionType
    let url: String?
}

enum ActionType: String, CaseIterable, Codable {
    case acknowledge = "Acknowledge"
    case respond = "Respond"
    case schedule = "Schedule Meeting"
    case contact = "Contact Teacher"
    case review = "Review Details"
    case approve = "Approve"
    case decline = "Decline"
}

struct ProgressOverview: Codable {
    let overallGrade: String
    let subjectProgress: [SubjectProgress]
    let skillDevelopment: [SkillProgress]
    let behavioralProgress: BehavioralProgress
    let socialProgress: SocialProgress
    let trends: [ProgressTrend]
}

struct SubjectProgress: Identifiable, Codable {
    let id: UUID
    let subject: String
    let currentGrade: String
    let progressPercentage: Double
    let timeSpent: Int
    let strengths: [String]
    let improvementAreas: [String]
    let recentAssessments: [AssessmentResult]
}

struct AssessmentResult: Identifiable, Codable {
    let id: UUID
    let name: String
    let score: Double
    let maxScore: Double
    let date: String
    let feedback: String
}

struct SkillProgress: Identifiable, Codable {
    let id: UUID
    let skillName: String
    let currentLevel: String
    let progressPercentage: Double
    let milestones: [Milestone]
    let nextGoals: [String]
}

struct Milestone: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let achievedDate: String?
    let isCompleted: Bool
}

struct BehavioralProgress: Codable {
    let overallRating: Double
    let positiveIncidents: Int
    let concerningIncidents: Int
    let improvementAreas: [String]
    let strengths: [String]
    let interventions: [Intervention]
}

struct Intervention: Identifiable, Codable {
    let id: UUID
    let type: String
    let description: String
    let startDate: String
    let effectiveness: Double?
    let status: InterventionStatus
}

enum InterventionStatus: String, CaseIterable, Codable {
    case planned = "Planned"
    case active = "Active"
    case completed = "Completed"
    case discontinued = "Discontinued"
}

struct SocialProgress: Codable {
    let peerInteractions: Double
    let communicationSkills: Double
    let collaborationSkills: Double
    let empathy: Double
    let socialGoals: [SocialGoal]
}

struct SocialGoal: Identifiable, Codable {
    let id: UUID
    let goal: String
    let progress: Double
    let targetDate: String
    let strategies: [String]
}

struct ProgressTrend: Identifiable, Codable {
    let id: UUID
    let metric: String
    let direction: TrendDirection
    let magnitude: Double
    let timeframe: String
    let significance: TrendSignificance
}

enum TrendDirection: String, CaseIterable, Codable {
    case improving = "Improving"
    case declining = "Declining"
    case stable = "Stable"
    case fluctuating = "Fluctuating"
}

enum TrendSignificance: String, CaseIterable, Codable {
    case significant = "Significant"
    case moderate = "Moderate"
    case minor = "Minor"
    case negligible = "Negligible"
}

struct CommunicationSummary: Codable {
    let unreadMessages: Int
    let recentConversations: [ConversationSummary]
    let scheduledMeetings: [ScheduledMeeting]
    let communicationHistory: [CommunicationRecord]
}

struct ConversationSummary: Identifiable, Codable {
    let id: UUID
    let participant: String
    let lastMessage: String
    let timestamp: String
    let isUnread: Bool
    let priority: MessagePriority
}

enum MessagePriority: String, CaseIterable, Codable {
    case low = "Low"
    case normal = "Normal"
    case high = "High"
    case urgent = "Urgent"
}

struct ScheduledMeeting: Identifiable, Codable {
    let id: UUID
    let title: String
    let participants: [String]
    let scheduledTime: String
    let duration: Int
    let agenda: [String]
    let meetingLink: String?
}

struct CommunicationRecord: Identifiable, Codable {
    let id: UUID
    let type: CommunicationType
    let participant: String
    let subject: String
    let timestamp: String
    let summary: String
}

enum CommunicationType: String, CaseIterable, Codable {
    case message = "Message"
    case email = "Email"
    case call = "Phone Call"
    case meeting = "Meeting"
    case note = "Note"
}

struct ParentRecommendation: Identifiable, Codable {
    let id: UUID
    let type: RecommendationType
    let title: String
    let description: String
    let priority: RecommendationPriority
    let actionItems: [String]
    let resources: [Resource]
    let timeline: String
}

enum RecommendationType: String, CaseIterable, Codable {
    case academic = "Academic Support"
    case behavioral = "Behavioral"
    case social = "Social Skills"
    case therapeutic = "Therapeutic"
    case enrichment = "Enrichment"
    case technology = "Technology"
}

enum RecommendationPriority: String, CaseIterable, Codable {
    case immediate = "Immediate"
    case soon = "Soon"
    case moderate = "Moderate"
    case low = "Low"
}

struct Resource: Identifiable, Codable {
    let id: UUID
    let title: String
    let type: ResourceType
    let url: String?
    let description: String
}

enum ResourceType: String, CaseIterable, Codable {
    case article = "Article"
    case video = "Video"
    case book = "Book"
    case website = "Website"
    case app = "App"
    case course = "Course"
    case support = "Support Group"
}

// MARK: - Additional Models for Phase 3 Parent Portal

// Update AlertType to include iconName and color
extension AlertType {
    var iconName: String {
        switch self {
        case .achievement: return "star.fill"
        case .concern: return "exclamationmark.triangle.fill"
        case .behavior: return "person.fill"
        case .academic: return "book.fill"
        case .health: return "heart.fill"
        case .attendance: return "calendar.fill"
        case .system: return "gear"
        }
    }

    var color: Color {
        switch self {
        case .achievement: return .orange
        case .concern: return .red
        case .behavior: return .blue
        case .academic: return .green
        case .health: return .pink
        case .attendance: return .purple
        case .system: return .gray
        }
    }
}

// Update CommunicationType to include iconName
extension CommunicationType {
    var iconName: String {
        switch self {
        case .message: return "message.fill"
        case .email: return "envelope.fill"
        case .call: return "phone.fill"
        case .meeting: return "calendar.badge.plus"
        case .note: return "note.text"
        }
    }
}

// Additional models needed for the Parent Portal Service
struct WeeklyProgress: Codable {
    let totalLearningTime: Int // minutes
    let lessonsCompleted: Int
    let achievementsEarned: Int
    let averageEngagement: Double
    let subjectBreakdown: [SubjectProgressSummary]
}

struct SubjectProgressSummary: Codable, Identifiable {
    var id = UUID()
    let subject: String
    let timeSpent: Int // minutes
    let progress: Double // 0.0 to 1.0

    init(subject: String, timeSpent: Int, progress: Double) {
        self.id = UUID()
        self.subject = subject
        self.timeSpent = timeSpent
        self.progress = progress
    }
}

struct StudentReport: Identifiable, Codable {
    var id = UUID()
    let studentId: UUID
    let period: ReportPeriod
    let generatedAt: Date
    var academicProgress: AcademicProgressReport?
    var behavioralInsights: BehavioralInsightsReport?
    var learningAnalytics: LearningAnalyticsReport?
    var recommendations: [String] = []

    init(studentId: UUID, period: ReportPeriod, generatedAt: Date) {
        self.id = UUID()
        self.studentId = studentId
        self.period = period
        self.generatedAt = generatedAt
    }
}

struct AcademicProgressReport: Codable {
    let overallGrade: String
    let subjectGrades: [String: String]
    let improvementAreas: [String]
    let strengths: [String]
}

struct BehavioralInsightsReport: Codable {
    let engagementLevel: Double
    let attentionSpan: Int // minutes
    let socialInteraction: Double
    let emotionalWellbeing: Double
    let adaptationToSpecialNeeds: Double
    let notes: [String]
}

struct LearningAnalyticsReport: Codable {
    let totalLearningTime: TimeInterval
    let averageSessionLength: TimeInterval
    let preferredLearningTimes: [String]
    let mostEngagingSubjects: [String]
    let challengingAreas: [String]
    let adaptiveRecommendations: [String]
}

struct ParentCommunication: Identifiable, Codable {
    var id = UUID()
    let parentId: UUID
    let recipientId: UUID
    let recipientType: RecipientType
    let subject: String
    let message: String
    let priority: CommunicationPriority
    let timestamp: Date
    var communicationType: CommunicationTypeExtended = .message
    var isRead: Bool = false
    var response: String?

    init(parentId: UUID, recipientId: UUID, recipientType: RecipientType, subject: String, message: String, priority: CommunicationPriority, timestamp: Date) {
        self.id = UUID()
        self.parentId = parentId
        self.recipientId = recipientId
        self.recipientType = recipientType
        self.subject = subject
        self.message = message
        self.priority = priority
        self.timestamp = timestamp
    }

    enum RecipientType: String, CaseIterable, Codable {
        case teacher = "Teacher"
        case administrator = "Administrator"
        case support = "Support"
    }

    enum CommunicationTypeExtended: String, CaseIterable, Codable {
        case message = "Message"
        case meetingRequest = "Meeting Request"
        case concern = "Concern"
        case feedback = "Feedback"

        var iconName: String {
            switch self {
            case .message: return "message.fill"
            case .meetingRequest: return "calendar.badge.plus"
            case .concern: return "exclamationmark.triangle.fill"
            case .feedback: return "star.fill"
            }
        }
    }
}

enum CommunicationPriority: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case urgent = "Urgent"
}

struct ParentNotificationSettings: Codable {
    var achievementNotifications: Bool = true
    var progressUpdates: Bool = true
    var teacherMessages: Bool = true
    var weeklyReports: Bool = true
    var behavioralAlerts: Bool = true
    var systemUpdates: Bool = false
    var emailNotifications: Bool = true
    var pushNotifications: Bool = true
    var smsNotifications: Bool = false
}

struct ParentPrivacySettings: Codable {
    var shareProgressWithSchool: Bool = true
    var allowDataAnalytics: Bool = true
    var thirdPartyIntegrations: Bool = false
    var anonymousUsageData: Bool = true
    var marketingCommunications: Bool = false
    var dataRetentionPeriod: DataRetentionPeriod = .oneYear

    enum DataRetentionPeriod: String, CaseIterable, Codable {
        case sixMonths = "6 Months"
        case oneYear = "1 Year"
        case twoYears = "2 Years"
        case indefinite = "Indefinite"
    }
}

enum ReportPeriod: String, CaseIterable, Codable {
    case weekly = "Weekly"
    case monthly = "Monthly"
    case quarterly = "Quarterly"
    case annual = "Annual"
}
