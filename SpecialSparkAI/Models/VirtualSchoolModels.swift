//
//  VirtualSchoolModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftData

// MARK: - Virtual School Environment Models

@Model
final class VirtualSchool {
    var id: UUID
    var name: String
    var motto: String
    var establishedDate: Date
    var campuses: [VirtualCampus]
    var totalStudents: Int
    var totalTeachers: Int
    var schoolColors: [String]
    var mascot: String
    var principalId: UUID?
    var isActive: Bool

    init(name: String, motto: String) {
        self.id = UUID()
        self.name = name
        self.motto = motto
        self.establishedDate = Date()
        self.campuses = []
        self.totalStudents = 0
        self.totalTeachers = 0
        self.schoolColors = ["#007AFF", "#34C759"] // Default blue and green
        self.mascot = "Spark the Phoenix"
        self.isActive = true
    }
}

@Model
final class VirtualCampus {
    var id: UUID
    var schoolId: UUID
    var name: String
    var gradeRange: String // e.g., "K-5", "6-8", "9-12"
    var buildings: [VirtualBuilding]
    var outdoorSpaces: [OutdoorSpace]
    var capacity: Int
    var currentOccupancy: Int
    var theme: CampusTheme

    init(schoolId: UUID, name: String, gradeRange: String, capacity: Int) {
        self.id = UUID()
        self.schoolId = schoolId
        self.name = name
        self.gradeRange = gradeRange
        self.buildings = []
        self.outdoorSpaces = []
        self.capacity = capacity
        self.currentOccupancy = 0
        self.theme = .modern
    }
}

enum CampusTheme: String, CaseIterable, Codable {
    case modern = "Modern"
    case traditional = "Traditional"
    case futuristic = "Futuristic"
    case nature = "Nature-Inspired"
    case castle = "Castle"
    case space = "Space Station"
    case underwater = "Underwater"
    case magical = "Magical"
}

@Model
final class VirtualBuilding {
    var id: UUID
    var campusId: UUID
    var name: String
    var buildingType: BuildingType
    var floors: [Floor]
    var capacity: Int
    var currentOccupancy: Int
    var accessibility: AccessibilityFeatures
    var isActive: Bool
    var buildingDescription: String

    init(campusId: UUID, name: String, buildingType: BuildingType, capacity: Int) {
        self.id = UUID()
        self.campusId = campusId
        self.name = name
        self.buildingType = buildingType
        self.floors = []
        self.capacity = capacity
        self.currentOccupancy = 0
        self.accessibility = AccessibilityFeatures()
        self.isActive = true
        self.buildingDescription = buildingType.rawValue
    }
}

enum BuildingType: String, CaseIterable, Codable {
    case academic = "Academic Building"
    case administration = "Administration"
    case library = "Library"
    case cafeteria = "Cafeteria"
    case gymnasium = "Gymnasium"
    case artCenter = "Art Center"
    case scienceLab = "Science Lab"
    case computerLab = "Computer Lab"
    case auditorium = "Auditorium"
    case specialEducation = "Special Education Center"
    case counseling = "Counseling Center"
    case medical = "Medical Center"
}

@Model
final class Floor {
    var id: UUID
    var buildingId: UUID
    var floorNumber: Int
    var rooms: [VirtualRoom]
    var hallways: [Hallway]
    var commonAreas: [CommonArea]
    var emergencyExits: [EmergencyExit]

    init(buildingId: UUID, floorNumber: Int) {
        self.id = UUID()
        self.buildingId = buildingId
        self.floorNumber = floorNumber
        self.rooms = []
        self.hallways = []
        self.commonAreas = []
        self.emergencyExits = []
    }
}

@Model
final class VirtualRoom {
    var id: UUID
    var floorId: UUID
    var roomNumber: String
    var name: String
    var type: RoomType
    var capacity: Int
    var currentOccupancy: Int
    var equipment: [Equipment]
    var layout: RoomLayout
    var accessibility: AccessibilityFeatures
    var ambiance: RoomAmbiance
    var isOccupied: Bool
    var currentActivity: String?

    init(floorId: UUID, roomNumber: String, name: String, type: RoomType, capacity: Int) {
        self.id = UUID()
        self.floorId = floorId
        self.roomNumber = roomNumber
        self.name = name
        self.type = type
        self.capacity = capacity
        self.currentOccupancy = 0
        self.equipment = []
        self.layout = RoomLayout()
        self.accessibility = AccessibilityFeatures()
        self.ambiance = RoomAmbiance()
        self.isOccupied = false
    }
}

enum RoomType: String, CaseIterable, Codable {
    case classroom = "Classroom"
    case laboratory = "Laboratory"
    case office = "Office"
    case conference = "Conference Room"
    case library = "Library"
    case cafeteria = "Cafeteria"
    case gymnasium = "Gymnasium"
    case artStudio = "Art Studio"
    case musicRoom = "Music Room"
    case computerLab = "Computer Lab"
    case specialEducation = "Special Education Room"
    case therapy = "Therapy Room"
    case quiet = "Quiet Room"
    case sensory = "Sensory Room"
    case breakRoom = "Break Room"
    case storage = "Storage"
}

@Model
final class Equipment {
    var id: UUID
    var roomId: UUID
    var name: String
    var type: EquipmentType
    var details: String
    var isWorking: Bool
    var isAccessible: Bool
    var lastMaintenance: Date
    var specialFeatures: [String]

    init(roomId: UUID, name: String, type: EquipmentType) {
        self.id = UUID()
        self.roomId = roomId
        self.name = name
        self.type = type
        self.details = ""
        self.isWorking = true
        self.isAccessible = true
        self.lastMaintenance = Date()
        self.specialFeatures = []
    }
}

enum EquipmentType: String, CaseIterable, Codable {
    case smartboard = "Smart Board"
    case computer = "Computer"
    case tablet = "Tablet"
    case projector = "Projector"
    case camera = "Camera"
    case microphone = "Microphone"
    case speakers = "Speakers"
    case desk = "Desk"
    case chair = "Chair"
    case whiteboard = "Whiteboard"
    case bookshelf = "Bookshelf"
    case artSupplies = "Art Supplies"
    case scienceEquipment = "Science Equipment"
    case musicInstrument = "Musical Instrument"
    case assistiveTechnology = "Assistive Technology"
    case sensoryTool = "Sensory Tool"
}

@Model
final class RoomLayout {
    var id: UUID
    var roomId: UUID
    var arrangement: LayoutArrangement
    var seatingCapacity: Int
    var hasFlexibleSeating: Bool
    var hasQuietZones: Bool
    var hasMovementAreas: Bool
    var isWheelchairAccessible: Bool
    var lightingOptions: [LightingOption]
    var soundOptions: [SoundOption]

    init(roomId: UUID = UUID()) {
        self.id = UUID()
        self.roomId = roomId
        self.arrangement = .traditional
        self.seatingCapacity = 25
        self.hasFlexibleSeating = true
        self.hasQuietZones = true
        self.hasMovementAreas = true
        self.isWheelchairAccessible = true
        self.lightingOptions = [.natural, .soft, .bright]
        self.soundOptions = [.quiet, .normal, .amplified]
    }
}

enum LayoutArrangement: String, CaseIterable, Codable {
    case traditional = "Traditional Rows"
    case circle = "Circle"
    case groups = "Small Groups"
    case uShape = "U-Shape"
    case flexible = "Flexible"
    case stations = "Learning Stations"
    case openSpace = "Open Space"
}

enum LightingOption: String, CaseIterable, Codable {
    case natural = "Natural Light"
    case soft = "Soft Light"
    case bright = "Bright Light"
    case colored = "Colored Light"
    case dimmed = "Dimmed Light"
}

enum SoundOption: String, CaseIterable, Codable {
    case quiet = "Quiet"
    case normal = "Normal"
    case amplified = "Amplified"
    case backgroundMusic = "Background Music"
    case natureSounds = "Nature Sounds"
    case whiteNoise = "White Noise"
}

@Model
final class RoomAmbiance {
    var id: UUID
    var roomId: UUID
    var temperature: Int // Fahrenheit
    var humidity: Int // Percentage
    var airQuality: AirQuality
    var noiseLevel: NoiseLevel
    var lighting: LightingOption
    var backgroundSound: SoundOption?
    var scent: String?
    var colorScheme: [String]

    init(roomId: UUID = UUID()) {
        self.id = UUID()
        self.roomId = roomId
        self.temperature = 72
        self.humidity = 45
        self.airQuality = .excellent
        self.noiseLevel = .quiet
        self.lighting = .natural
        self.colorScheme = ["#F0F8FF", "#E6F3FF"] // Light blue tones
    }
}

enum AirQuality: String, CaseIterable, Codable {
    case excellent = "Excellent"
    case good = "Good"
    case fair = "Fair"
    case poor = "Poor"
}

enum NoiseLevel: String, CaseIterable, Codable {
    case silent = "Silent"
    case quiet = "Quiet"
    case moderate = "Moderate"
    case loud = "Loud"
}

@Model
final class AccessibilityFeatures {
    var id: UUID
    var entityId: UUID // Can be room, building, etc.
    var hasRamps: Bool
    var hasElevators: Bool
    var hasWideDoorsways: Bool
    var hasAccessibleRestrooms: Bool
    var hasVisualAlerts: Bool
    var hasAudioDescriptions: Bool
    var hasSignLanguageSupport: Bool
    var hasBrailleSignage: Bool
    var hasAssistiveListening: Bool
    var hasAdjustableDesks: Bool
    var hasSpecialSeating: Bool
    var hasSensorySupport: Bool

    init(entityId: UUID = UUID()) {
        self.id = UUID()
        self.entityId = entityId
        self.hasRamps = true
        self.hasElevators = true
        self.hasWideDoorsways = true
        self.hasAccessibleRestrooms = true
        self.hasVisualAlerts = true
        self.hasAudioDescriptions = true
        self.hasSignLanguageSupport = true
        self.hasBrailleSignage = true
        self.hasAssistiveListening = true
        self.hasAdjustableDesks = true
        self.hasSpecialSeating = true
        self.hasSensorySupport = true
    }
}

@Model
final class OutdoorSpace {
    var id: UUID
    var campusId: UUID
    var name: String
    var type: OutdoorSpaceType
    var capacity: Int
    var activities: [String]
    var accessibility: AccessibilityFeatures
    var weather: WeatherCondition
    var isAvailable: Bool

    init(campusId: UUID, name: String, type: OutdoorSpaceType) {
        self.id = UUID()
        self.campusId = campusId
        self.name = name
        self.type = type
        self.capacity = 50
        self.activities = []
        self.accessibility = AccessibilityFeatures()
        self.weather = .sunny
        self.isAvailable = true
    }
}

enum OutdoorSpaceType: String, CaseIterable, Codable {
    case playground = "Playground"
    case garden = "Garden"
    case sportsField = "Sports Field"
    case courtyard = "Courtyard"
    case amphitheater = "Amphitheater"
    case walkingPath = "Walking Path"
    case picnicArea = "Picnic Area"
    case greenhouse = "Greenhouse"
}

enum WeatherCondition: String, CaseIterable, Codable {
    case sunny = "Sunny"
    case cloudy = "Cloudy"
    case rainy = "Rainy"
    case snowy = "Snowy"
    case windy = "Windy"
    case perfect = "Perfect"
}

@Model
final class Hallway {
    var id: UUID
    var floorId: UUID
    var name: String
    var width: Double // feet
    var length: Double // feet
    var lockers: [Locker]
    var artwork: [Artwork]
    var bulletinBoards: [BulletinBoard]
    var accessibility: AccessibilityFeatures
    var crowdLevel: CrowdLevel

    init(floorId: UUID, name: String, width: Double, length: Double) {
        self.id = UUID()
        self.floorId = floorId
        self.name = name
        self.width = width
        self.length = length
        self.lockers = []
        self.artwork = []
        self.bulletinBoards = []
        self.accessibility = AccessibilityFeatures()
        self.crowdLevel = .low
    }
}

enum CrowdLevel: String, CaseIterable, Codable {
    case empty = "Empty"
    case low = "Low"
    case moderate = "Moderate"
    case high = "High"
    case crowded = "Crowded"
}

@Model
final class CommonArea {
    var id: UUID
    var floorId: UUID
    var name: String
    var type: CommonAreaType
    var capacity: Int
    var currentOccupancy: Int
    var activities: [String]
    var furniture: [Furniture]
    var ambiance: RoomAmbiance
    var isQuietZone: Bool

    init(floorId: UUID, name: String, type: CommonAreaType) {
        self.id = UUID()
        self.floorId = floorId
        self.name = name
        self.type = type
        self.capacity = 30
        self.currentOccupancy = 0
        self.activities = []
        self.furniture = []
        self.ambiance = RoomAmbiance()
        self.isQuietZone = false
    }
}

enum CommonAreaType: String, CaseIterable, Codable {
    case lobby = "Lobby"
    case lounge = "Lounge"
    case studyArea = "Study Area"
    case socialArea = "Social Area"
    case readingNook = "Reading Nook"
    case gameArea = "Game Area"
    case quietZone = "Quiet Zone"
    case collaborationSpace = "Collaboration Space"
}

@Model
final class Furniture {
    var id: UUID
    var areaId: UUID
    var name: String
    var type: FurnitureType
    var isOccupied: Bool
    var isAccessible: Bool
    var comfort: Int // 1-10 scale
    var condition: ItemCondition

    init(areaId: UUID, name: String, type: FurnitureType) {
        self.id = UUID()
        self.areaId = areaId
        self.name = name
        self.type = type
        self.isOccupied = false
        self.isAccessible = true
        self.comfort = 8
        self.condition = .excellent
    }
}

enum FurnitureType: String, CaseIterable, Codable {
    case chair = "Chair"
    case sofa = "Sofa"
    case table = "Table"
    case desk = "Desk"
    case bookshelf = "Bookshelf"
    case beanbag = "Bean Bag"
    case bench = "Bench"
    case stool = "Stool"
}

enum ItemCondition: String, CaseIterable, Codable {
    case excellent = "Excellent"
    case good = "Good"
    case fair = "Fair"
    case poor = "Poor"
    case needsRepair = "Needs Repair"
}

@Model
final class Locker {
    var id: UUID
    var hallwayId: UUID
    var number: String
    var studentId: UUID?
    var isAssigned: Bool
    var isAccessible: Bool
    var condition: ItemCondition
    var contents: [String]

    init(hallwayId: UUID, number: String) {
        self.id = UUID()
        self.hallwayId = hallwayId
        self.number = number
        self.isAssigned = false
        self.isAccessible = true
        self.condition = .excellent
        self.contents = []
    }
}

@Model
final class Artwork {
    var id: UUID
    var locationId: UUID // Can be hallway, room, etc.
    var title: String
    var artist: String
    var type: ArtworkType
    var details: String
    var isStudentWork: Bool
    var dateCreated: Date
    var isInteractive: Bool

    init(locationId: UUID, title: String, artist: String, type: ArtworkType) {
        self.id = UUID()
        self.locationId = locationId
        self.title = title
        self.artist = artist
        self.type = type
        self.details = ""
        self.isStudentWork = false
        self.dateCreated = Date()
        self.isInteractive = false
    }
}

enum ArtworkType: String, CaseIterable, Codable {
    case painting = "Painting"
    case sculpture = "Sculpture"
    case photography = "Photography"
    case digital = "Digital Art"
    case mural = "Mural"
    case mosaic = "Mosaic"
    case installation = "Installation"
    case poster = "Poster"
}

@Model
final class BulletinBoard {
    var id: UUID
    var locationId: UUID
    var title: String
    var content: [BulletinItem]
    var theme: String
    var lastUpdated: Date
    var isInteractive: Bool
    var targetAudience: TargetAudience

    init(locationId: UUID, title: String) {
        self.id = UUID()
        self.locationId = locationId
        self.title = title
        self.content = []
        self.theme = "General"
        self.lastUpdated = Date()
        self.isInteractive = false
        self.targetAudience = .students
    }
}

enum TargetAudience: String, CaseIterable, Codable {
    case students = "Students"
    case parents = "Parents"
    case teachers = "Teachers"
    case all = "All"
}

@Model
final class BulletinItem {
    var id: UUID
    var bulletinBoardId: UUID
    var title: String
    var content: String
    var type: BulletinItemType
    var priority: Priority
    var expirationDate: Date?
    var authorId: UUID
    var isApproved: Bool

    init(bulletinBoardId: UUID, title: String, content: String, type: BulletinItemType, authorId: UUID) {
        self.id = UUID()
        self.bulletinBoardId = bulletinBoardId
        self.title = title
        self.content = content
        self.type = type
        self.priority = .normal
        self.authorId = authorId
        self.isApproved = true
    }
}

enum BulletinItemType: String, CaseIterable, Codable {
    case announcement = "Announcement"
    case event = "Event"
    case achievement = "Achievement"
    case reminder = "Reminder"
    case news = "News"
    case celebration = "Celebration"
}

enum Priority: String, CaseIterable, Codable {
    case low = "Low"
    case normal = "Normal"
    case high = "High"
    case urgent = "Urgent"
}

@Model
final class EmergencyExit {
    var id: UUID
    var floorId: UUID
    var location: String
    var isAccessible: Bool
    var isWorking: Bool
    var lastInspection: Date
    var exitType: ExitType

    init(floorId: UUID, location: String, exitType: ExitType) {
        self.id = UUID()
        self.floorId = floorId
        self.location = location
        self.isAccessible = true
        self.isWorking = true
        self.lastInspection = Date()
        self.exitType = exitType
    }
}

enum ExitType: String, CaseIterable, Codable {
    case door = "Door"
    case stairway = "Stairway"
    case elevator = "Elevator"
    case ramp = "Ramp"
    case window = "Window"
}
