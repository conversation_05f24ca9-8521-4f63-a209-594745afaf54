//
//  CreativeArtsModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Creative Arts Models

struct ArtProject: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let artForm: ArtForm
    let gradeLevel: GradeLevel
    let difficulty: DifficultyLevel
    let duration: Int // minutes
    let objectives: [String]
    let materials: [ArtMaterial]
    let techniques: [ArtTechnique]
    let steps: [ArtStep]
    let inspirations: [ArtInspiration]
    let vocabulary: [ArtVocabulary]
    let assessmentCriteria: [AssessmentCriterion]
    let adaptations: [SpecialNeedsAdaptation]
    let extensions: [String]
    let isActive: Bool
    
    init(title: String, description: String, artForm: ArtForm, gradeLevel: GradeLevel) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.artForm = artForm
        self.gradeLevel = gradeLevel
        self.difficulty = .beginner
        self.duration = 60
        self.objectives = []
        self.materials = []
        self.techniques = []
        self.steps = []
        self.inspirations = []
        self.vocabulary = []
        self.assessmentCriteria = []
        self.adaptations = []
        self.extensions = []
        self.isActive = true
    }
}

enum ArtForm: String, CaseIterable, Codable {
    case drawing = "drawing"
    case painting = "painting"
    case sculpture = "sculpture"
    case printmaking = "printmaking"
    case photography = "photography"
    case digitalArt = "digital_art"
    case mixedMedia = "mixed_media"
    case ceramics = "ceramics"
    case textiles = "textiles"
    case music = "music"
    case dance = "dance"
    case theater = "theater"
    case creative_writing = "creative_writing"
    case filmmaking = "filmmaking"
    case animation = "animation"
    
    var displayName: String {
        switch self {
        case .drawing: return "Drawing"
        case .painting: return "Painting"
        case .sculpture: return "Sculpture"
        case .printmaking: return "Printmaking"
        case .photography: return "Photography"
        case .digitalArt: return "Digital Art"
        case .mixedMedia: return "Mixed Media"
        case .ceramics: return "Ceramics"
        case .textiles: return "Textiles"
        case .music: return "Music"
        case .dance: return "Dance"
        case .theater: return "Theater"
        case .creative_writing: return "Creative Writing"
        case .filmmaking: return "Filmmaking"
        case .animation: return "Animation"
        }
    }
    
    var icon: String {
        switch self {
        case .drawing: return "pencil.tip"
        case .painting: return "paintbrush.fill"
        case .sculpture: return "cube.fill"
        case .printmaking: return "printer.fill"
        case .photography: return "camera.fill"
        case .digitalArt: return "ipad.and.apple.pencil"
        case .mixedMedia: return "rectangle.3.group.fill"
        case .ceramics: return "circle.fill"
        case .textiles: return "scissors"
        case .music: return "music.note"
        case .dance: return "figure.dance"
        case .theater: return "theatermasks.fill"
        case .creative_writing: return "text.book.closed.fill"
        case .filmmaking: return "video.fill"
        case .animation: return "play.rectangle.fill"
        }
    }
}

struct ArtMaterial: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let quantity: String
    let isOptional: Bool
    let alternatives: [String]
    let safetyNotes: String
    let cost: MaterialCost
    let ageAppropriate: Bool
    
    init(name: String, quantity: String, isOptional: Bool = false) {
        self.id = UUID()
        self.name = name
        self.description = ""
        self.quantity = quantity
        self.isOptional = isOptional
        self.alternatives = []
        self.safetyNotes = ""
        self.cost = .low
        self.ageAppropriate = true
    }
}

struct ArtTechnique: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let difficulty: DifficultyLevel
    let instructions: [String]
    let tips: [String]
    let commonMistakes: [String]
    let videoURL: String?
    let imageURL: String?
    
    init(name: String, description: String, difficulty: DifficultyLevel) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.difficulty = difficulty
        self.instructions = []
        self.tips = []
        self.commonMistakes = []
        self.videoURL = nil
        self.imageURL = nil
    }
}

struct ArtStep: Identifiable, Codable {
    let id: UUID
    let stepNumber: Int
    let title: String
    let instruction: String
    let estimatedTime: Int // minutes
    let techniques: [UUID] // technique IDs
    let materials: [UUID] // material IDs
    let tips: [String]
    let imageURL: String?
    let videoURL: String?
    
    init(stepNumber: Int, title: String, instruction: String, estimatedTime: Int) {
        self.id = UUID()
        self.stepNumber = stepNumber
        self.title = title
        self.instruction = instruction
        self.estimatedTime = estimatedTime
        self.techniques = []
        self.materials = []
        self.tips = []
        self.imageURL = nil
        self.videoURL = nil
    }
}

struct ArtInspiration: Identifiable, Codable {
    let id: UUID
    let title: String
    let artist: String?
    let period: String?
    let style: String?
    let description: String
    let imageURL: String?
    let relevance: String
    
    init(title: String, description: String, relevance: String) {
        self.id = UUID()
        self.title = title
        self.artist = nil
        self.period = nil
        self.style = nil
        self.description = description
        self.imageURL = nil
        self.relevance = relevance
    }
}

struct ArtVocabulary: Identifiable, Codable {
    let id: UUID
    let term: String
    let definition: String
    let example: String?
    let imageURL: String?
    let relatedTerms: [String]
    
    init(term: String, definition: String) {
        self.id = UUID()
        self.term = term
        self.definition = definition
        self.example = nil
        self.imageURL = nil
        self.relatedTerms = []
    }
}

struct SpecialNeedsAdaptation: Identifiable, Codable {
    let id: UUID
    let needType: SpecialNeedType
    let adaptation: String
    let materials: [String]
    let instructions: [String]
    let benefits: [String]
    
    init(needType: SpecialNeedType, adaptation: String) {
        self.id = UUID()
        self.needType = needType
        self.adaptation = adaptation
        self.materials = []
        self.instructions = []
        self.benefits = []
    }
}

// MARK: - Student Art Activity

struct StudentArtActivity: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let projectId: UUID
    let startDate: Date
    let completionDate: Date?
    let status: ArtProjectStatus
    let progress: [ArtProgress]
    let reflections: [ArtReflection]
    let photos: [String] // URLs of work in progress
    let finalArtworkURL: String?
    let selfAssessment: SelfAssessment?
    let teacherFeedback: TeacherFeedback?
    let peerFeedback: [PeerFeedback]
    let grade: Double?
    let displayInGallery: Bool
    
    init(studentId: UUID, projectId: UUID) {
        self.id = UUID()
        self.studentId = studentId
        self.projectId = projectId
        self.startDate = Date()
        self.completionDate = nil
        self.status = .notStarted
        self.progress = []
        self.reflections = []
        self.photos = []
        self.finalArtworkURL = nil
        self.selfAssessment = nil
        self.teacherFeedback = nil
        self.peerFeedback = []
        self.grade = nil
        self.displayInGallery = false
    }
}

enum ArtProjectStatus: String, CaseIterable, Codable {
    case notStarted = "not_started"
    case planning = "planning"
    case inProgress = "in_progress"
    case completed = "completed"
    case needsRevision = "needs_revision"
    case submitted = "submitted"
    case graded = "graded"
    case displayed = "displayed"
    
    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .planning: return "Planning"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .needsRevision: return "Needs Revision"
        case .submitted: return "Submitted"
        case .graded: return "Graded"
        case .displayed: return "On Display"
        }
    }
}

struct ArtProgress: Identifiable, Codable {
    let id: UUID
    let stepId: UUID
    let completedAt: Date
    let timeSpent: Int // minutes
    let notes: String
    let challenges: [String]
    let successes: [String]
    let photoURL: String?
    
    init(stepId: UUID, timeSpent: Int, notes: String) {
        self.id = UUID()
        self.stepId = stepId
        self.completedAt = Date()
        self.timeSpent = timeSpent
        self.notes = notes
        self.challenges = []
        self.successes = []
        self.photoURL = nil
    }
}

struct ArtReflection: Identifiable, Codable {
    let id: UUID
    let prompt: String
    let response: String
    let createdAt: Date
    let mood: String?
    let inspiration: String?
    
    init(prompt: String, response: String) {
        self.id = UUID()
        self.prompt = prompt
        self.response = response
        self.createdAt = Date()
        self.mood = nil
        self.inspiration = nil
    }
}

struct SelfAssessment: Codable {
    let effort: Int // 1-5 scale
    let creativity: Int // 1-5 scale
    let technique: Int // 1-5 scale
    let satisfaction: Int // 1-5 scale
    let learningGoals: String
    let improvements: String
    let proudMoments: String
    let nextSteps: String
    
    init() {
        self.effort = 3
        self.creativity = 3
        self.technique = 3
        self.satisfaction = 3
        self.learningGoals = ""
        self.improvements = ""
        self.proudMoments = ""
        self.nextSteps = ""
    }
}

struct TeacherFeedback: Codable {
    let strengths: [String]
    let improvements: [String]
    let techniques: [String]
    let creativity: String
    let effort: String
    let nextSteps: [String]
    let overallComment: String
    let grade: Double?
    let createdAt: Date
    
    init() {
        self.strengths = []
        self.improvements = []
        self.techniques = []
        self.creativity = ""
        self.effort = ""
        self.nextSteps = []
        self.overallComment = ""
        self.grade = nil
        self.createdAt = Date()
    }
}

struct PeerFeedback: Identifiable, Codable {
    let id: UUID
    let fromStudentId: UUID
    let comment: String
    let likes: [String]
    let suggestions: [String]
    let createdAt: Date
    
    init(fromStudentId: UUID, comment: String) {
        self.id = UUID()
        self.fromStudentId = fromStudentId
        self.comment = comment
        self.likes = []
        self.suggestions = []
        self.createdAt = Date()
    }
}
