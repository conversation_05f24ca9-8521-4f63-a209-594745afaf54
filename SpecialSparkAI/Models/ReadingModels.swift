//
//  ReadingModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Reading Activity Models

struct Book: Identifiable, Codable {
    let id: UUID
    let title: String
    let author: String
    let isbn: String?
    let description: String
    let coverImageURL: String?
    let gradeLevel: GradeLevel
    let readingLevel: ReadingLevel
    let genre: BookGenre
    let pageCount: Int
    let estimatedReadingTime: Int // minutes
    let language: String
    let publisher: String?
    let publishedDate: Date?
    let tags: [String]
    let isRecommended: Bool
    let isAvailable: Bool
    let contentURL: String? // for digital books

    init(title: String, author: String, gradeLevel: GradeLevel, genre: BookGenre, pageCount: Int, isRecommended: Bool = false) {
        self.id = UUID()
        self.title = title
        self.author = author
        self.isbn = nil
        self.description = ""
        self.coverImageURL = nil
        self.gradeLevel = gradeLevel
        self.readingLevel = .gradeLevel
        self.genre = genre
        self.pageCount = pageCount
        self.estimatedReadingTime = pageCount * 2 // 2 minutes per page average
        self.language = "English"
        self.publisher = nil
        self.publishedDate = nil
        self.tags = []
        self.isRecommended = isRecommended
        self.isAvailable = true
        self.contentURL = nil
    }
}

enum ReadingLevel: String, CaseIterable, Codable {
    case emergent = "emergent"
    case beginning = "beginning"
    case developing = "developing"
    case fluent = "fluent"
    case advanced = "advanced"
    case gradeLevel = "grade_level"
    case aboveGrade = "above_grade"
    case belowGrade = "below_grade"

    var displayName: String {
        switch self {
        case .emergent: return "Emergent Reader"
        case .beginning: return "Beginning Reader"
        case .developing: return "Developing Reader"
        case .fluent: return "Fluent Reader"
        case .advanced: return "Advanced Reader"
        case .gradeLevel: return "Grade Level"
        case .aboveGrade: return "Above Grade Level"
        case .belowGrade: return "Below Grade Level"
        }
    }
}

enum BookGenre: String, CaseIterable, Codable {
    case fiction = "fiction"
    case nonFiction = "non_fiction"
    case biography = "biography"
    case mystery = "mystery"
    case adventure = "adventure"
    case fantasy = "fantasy"
    case scienceFiction = "science_fiction"
    case historical = "historical"
    case poetry = "poetry"
    case drama = "drama"
    case informational = "informational"
    case howTo = "how_to"
    case reference = "reference"
    case textbook = "textbook"
    case pictureBook = "picture_book"
    case graphicNovel = "graphic_novel"

    var displayName: String {
        switch self {
        case .fiction: return "Fiction"
        case .nonFiction: return "Non-Fiction"
        case .biography: return "Biography"
        case .mystery: return "Mystery"
        case .adventure: return "Adventure"
        case .fantasy: return "Fantasy"
        case .scienceFiction: return "Science Fiction"
        case .historical: return "Historical"
        case .poetry: return "Poetry"
        case .drama: return "Drama"
        case .informational: return "Informational"
        case .howTo: return "How-To"
        case .reference: return "Reference"
        case .textbook: return "Textbook"
        case .pictureBook: return "Picture Book"
        case .graphicNovel: return "Graphic Novel"
        }
    }
}

struct ReadingActivity: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let bookId: UUID
    let startDate: Date
    let targetCompletionDate: Date?
    let actualCompletionDate: Date?
    let currentPage: Int
    let totalPages: Int
    let readingGoal: ReadingGoal
    let sessions: [ReadingSession]
    let comprehensionQuizzes: [ComprehensionQuiz]
    let notes: [ReadingNote]
    let status: ReadingStatus

    init(studentId: UUID, bookId: UUID, totalPages: Int, goal: ReadingGoal) {
        self.id = UUID()
        self.studentId = studentId
        self.bookId = bookId
        self.startDate = Date()
        self.targetCompletionDate = nil
        self.actualCompletionDate = nil
        self.currentPage = 0
        self.totalPages = totalPages
        self.readingGoal = goal
        self.sessions = []
        self.comprehensionQuizzes = []
        self.notes = []
        self.status = .notStarted
    }
}

enum ReadingGoal: String, CaseIterable, Codable {
    case enjoyment = "enjoyment"
    case comprehension = "comprehension"
    case vocabulary = "vocabulary"
    case fluency = "fluency"
    case analysis = "analysis"
    case research = "research"
    case assignment = "assignment"

    var displayName: String {
        switch self {
        case .enjoyment: return "Reading for Enjoyment"
        case .comprehension: return "Reading Comprehension"
        case .vocabulary: return "Vocabulary Building"
        case .fluency: return "Reading Fluency"
        case .analysis: return "Literary Analysis"
        case .research: return "Research & Information"
        case .assignment: return "School Assignment"
        }
    }
}

enum ReadingStatus: String, CaseIterable, Codable {
    case notStarted = "not_started"
    case inProgress = "in_progress"
    case completed = "completed"
    case paused = "paused"
    case abandoned = "abandoned"

    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .paused: return "Paused"
        case .abandoned: return "Abandoned"
        }
    }
}

struct ReadingSession: Identifiable, Codable {
    let id: UUID
    let startTime: Date
    let endTime: Date?
    let pagesRead: Int
    let startPage: Int
    let endPage: Int
    let readingSpeed: Double? // words per minute
    let comprehensionScore: Double? // percentage
    let enjoymentRating: Int? // 1-5 stars
    let notes: String
    let distractions: [String]
    let readingEnvironment: ReadingEnvironment

    init(startPage: Int, endPage: Int) {
        self.id = UUID()
        self.startTime = Date()
        self.endTime = nil
        self.pagesRead = endPage - startPage
        self.startPage = startPage
        self.endPage = endPage
        self.readingSpeed = nil
        self.comprehensionScore = nil
        self.enjoymentRating = nil
        self.notes = ""
        self.distractions = []
        self.readingEnvironment = ReadingEnvironment()
    }
}

struct ReadingEnvironment: Codable {
    let location: String
    let lighting: String
    let noiseLevel: String
    let temperature: String
    let posture: String
    let deviceUsed: String?

    init() {
        self.location = "Unknown"
        self.lighting = "Good"
        self.noiseLevel = "Quiet"
        self.temperature = "Comfortable"
        self.posture = "Sitting"
        self.deviceUsed = nil
    }
}

struct ComprehensionQuiz: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let questions: [ComprehensionQuestion]
    let pageRange: String // e.g., "1-25"
    let timeLimit: Int? // minutes
    let score: Double? // percentage
    let completedAt: Date?

    init(title: String, pageRange: String) {
        self.id = UUID()
        self.title = title
        self.description = ""
        self.questions = []
        self.pageRange = pageRange
        self.timeLimit = nil
        self.score = nil
        self.completedAt = nil
    }
}

struct ComprehensionQuestion: Identifiable, Codable {
    let id: UUID
    let question: String
    let type: QuestionType
    let options: [String]
    let correctAnswer: String
    let explanation: String
    let difficulty: DifficultyLevel
    let skillTested: ComprehensionSkill

    init(question: String, type: QuestionType, options: [String], correctAnswer: String, skill: ComprehensionSkill) {
        self.id = UUID()
        self.question = question
        self.type = type
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = ""
        self.difficulty = .beginner
        self.skillTested = skill
    }
}

enum QuestionType: String, CaseIterable, Codable {
    case multipleChoice = "multiple_choice"
    case trueFalse = "true_false"
    case shortAnswer = "short_answer"
    case essay = "essay"
    case fillInBlank = "fill_in_blank"
    case matching = "matching"
    case ordering = "ordering"

    var displayName: String {
        switch self {
        case .multipleChoice: return "Multiple Choice"
        case .trueFalse: return "True/False"
        case .shortAnswer: return "Short Answer"
        case .essay: return "Essay"
        case .fillInBlank: return "Fill in the Blank"
        case .matching: return "Matching"
        case .ordering: return "Ordering"
        }
    }
}

enum ComprehensionSkill: String, CaseIterable, Codable {
    case mainIdea = "main_idea"
    case details = "details"
    case inference = "inference"
    case vocabulary = "vocabulary"
    case sequence = "sequence"
    case causeEffect = "cause_effect"
    case compare = "compare"
    case characterAnalysis = "character_analysis"
    case theme = "theme"
    case prediction = "prediction"

    var displayName: String {
        switch self {
        case .mainIdea: return "Main Idea"
        case .details: return "Supporting Details"
        case .inference: return "Making Inferences"
        case .vocabulary: return "Vocabulary"
        case .sequence: return "Sequence of Events"
        case .causeEffect: return "Cause and Effect"
        case .compare: return "Compare and Contrast"
        case .characterAnalysis: return "Character Analysis"
        case .theme: return "Theme"
        case .prediction: return "Making Predictions"
        }
    }
}

struct ReadingNote: Identifiable, Codable {
    let id: UUID
    let page: Int
    let content: String
    let type: NoteType
    let createdAt: Date
    let tags: [String]

    init(page: Int, content: String, type: NoteType) {
        self.id = UUID()
        self.page = page
        self.content = content
        self.type = type
        self.createdAt = Date()
        self.tags = []
    }
}

enum NoteType: String, CaseIterable, Codable {
    case highlight = "highlight"
    case question = "question"
    case summary = "summary"
    case vocabulary = "vocabulary"
    case connection = "connection"
    case prediction = "prediction"
    case reflection = "reflection"

    var displayName: String {
        switch self {
        case .highlight: return "Highlight"
        case .question: return "Question"
        case .summary: return "Summary"
        case .vocabulary: return "Vocabulary"
        case .connection: return "Connection"
        case .prediction: return "Prediction"
        case .reflection: return "Reflection"
        }
    }
}
