//
//  ScienceExperimentModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Science Experiment Models

struct ScienceExperiment: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let objective: String
    let hypothesis: String
    let gradeLevel: GradeLevel
    let scienceDomain: ScienceDomain
    let difficulty: DifficultyLevel
    let duration: Int // minutes
    let safetyLevel: SafetyLevel
    let materials: [Material]
    let procedures: [Procedure]
    let observations: [ObservationPoint]
    let expectedResults: String
    let explanation: String
    let extensions: [String]
    let vocabulary: [VocabularyTerm]
    let standards: [String] // curriculum standards
    let isVirtual: Bool
    let simulationURL: String?
    let videoURL: String?
    let isActive: Bool
    
    init(title: String, description: String, objective: String, gradeLevel: GradeLevel, domain: ScienceDomain) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.objective = objective
        self.hypothesis = ""
        self.gradeLevel = gradeLevel
        self.scienceDomain = domain
        self.difficulty = .beginner
        self.duration = 45
        self.safetyLevel = .low
        self.materials = []
        self.procedures = []
        self.observations = []
        self.expectedResults = ""
        self.explanation = ""
        self.extensions = []
        self.vocabulary = []
        self.standards = []
        self.isVirtual = false
        self.simulationURL = nil
        self.videoURL = nil
        self.isActive = true
    }
}

enum ScienceDomain: String, CaseIterable, Codable {
    case physics = "physics"
    case chemistry = "chemistry"
    case biology = "biology"
    case earthScience = "earth_science"
    case astronomy = "astronomy"
    case environmental = "environmental"
    case engineering = "engineering"
    case technology = "technology"
    case mathematics = "mathematics"
    case interdisciplinary = "interdisciplinary"
    
    var displayName: String {
        switch self {
        case .physics: return "Physics"
        case .chemistry: return "Chemistry"
        case .biology: return "Biology"
        case .earthScience: return "Earth Science"
        case .astronomy: return "Astronomy"
        case .environmental: return "Environmental Science"
        case .engineering: return "Engineering"
        case .technology: return "Technology"
        case .mathematics: return "Mathematics"
        case .interdisciplinary: return "Interdisciplinary"
        }
    }
}

enum SafetyLevel: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case adultSupervision = "adult_supervision"
    
    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        case .adultSupervision: return "Adult Supervision Required"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .adultSupervision: return .red
        }
    }
}

struct Material: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let quantity: String
    let isOptional: Bool
    let safetyNotes: String
    let alternatives: [String]
    let cost: MaterialCost
    let availability: MaterialAvailability
    
    init(name: String, quantity: String, isOptional: Bool = false) {
        self.id = UUID()
        self.name = name
        self.description = ""
        self.quantity = quantity
        self.isOptional = isOptional
        self.safetyNotes = ""
        self.alternatives = []
        self.cost = .low
        self.availability = .common
    }
}

enum MaterialCost: String, CaseIterable, Codable {
    case free = "free"
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .free: return "Free"
        case .low: return "Low Cost ($1-5)"
        case .medium: return "Medium Cost ($6-20)"
        case .high: return "High Cost ($20+)"
        }
    }
}

enum MaterialAvailability: String, CaseIterable, Codable {
    case common = "common"
    case uncommon = "uncommon"
    case specialized = "specialized"
    case rare = "rare"
    
    var displayName: String {
        switch self {
        case .common: return "Common (household items)"
        case .uncommon: return "Uncommon (store purchase)"
        case .specialized: return "Specialized (science supply)"
        case .rare: return "Rare (special order)"
        }
    }
}

struct Procedure: Identifiable, Codable {
    let id: UUID
    let stepNumber: Int
    let instruction: String
    let safetyWarning: String?
    let expectedOutcome: String?
    let troubleshooting: String?
    let imageURL: String?
    let videoURL: String?
    let estimatedTime: Int? // minutes
    
    init(stepNumber: Int, instruction: String) {
        self.id = UUID()
        self.stepNumber = stepNumber
        self.instruction = instruction
        self.safetyWarning = nil
        self.expectedOutcome = nil
        self.troubleshooting = nil
        self.imageURL = nil
        self.videoURL = nil
        self.estimatedTime = nil
    }
}

struct ObservationPoint: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: ObservationType
    let measurementUnit: String?
    let expectedRange: String?
    let recordingMethod: RecordingMethod
    
    init(title: String, description: String, type: ObservationType) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.type = type
        self.measurementUnit = nil
        self.expectedRange = nil
        self.recordingMethod = .written
    }
}

enum ObservationType: String, CaseIterable, Codable {
    case qualitative = "qualitative"
    case quantitative = "quantitative"
    case visual = "visual"
    case auditory = "auditory"
    case tactile = "tactile"
    case olfactory = "olfactory"
    case temporal = "temporal"
    
    var displayName: String {
        switch self {
        case .qualitative: return "Qualitative"
        case .quantitative: return "Quantitative"
        case .visual: return "Visual"
        case .auditory: return "Auditory"
        case .tactile: return "Tactile"
        case .olfactory: return "Smell"
        case .temporal: return "Time-based"
        }
    }
}

enum RecordingMethod: String, CaseIterable, Codable {
    case written = "written"
    case drawing = "drawing"
    case photo = "photo"
    case video = "video"
    case audio = "audio"
    case measurement = "measurement"
    case chart = "chart"
    case graph = "graph"
    
    var displayName: String {
        switch self {
        case .written: return "Written Notes"
        case .drawing: return "Drawing/Sketch"
        case .photo: return "Photograph"
        case .video: return "Video Recording"
        case .audio: return "Audio Recording"
        case .measurement: return "Measurements"
        case .chart: return "Data Chart"
        case .graph: return "Graph"
        }
    }
}

struct VocabularyTerm: Identifiable, Codable {
    let id: UUID
    let term: String
    let definition: String
    let pronunciation: String?
    let example: String?
    let relatedTerms: [String]
    
    init(term: String, definition: String) {
        self.id = UUID()
        self.term = term
        self.definition = definition
        self.pronunciation = nil
        self.example = nil
        self.relatedTerms = []
    }
}

// MARK: - Student Experiment Activity

struct StudentExperimentActivity: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let experimentId: UUID
    let startDate: Date
    let completionDate: Date?
    let status: ExperimentStatus
    let hypothesis: String
    let observations: [StudentObservation]
    let results: String
    let conclusion: String
    let reflection: String
    let photos: [String] // URLs
    let videos: [String] // URLs
    let labPartner: UUID? // another student
    let teacherFeedback: String?
    let grade: Double?
    
    init(studentId: UUID, experimentId: UUID) {
        self.id = UUID()
        self.studentId = studentId
        self.experimentId = experimentId
        self.startDate = Date()
        self.completionDate = nil
        self.status = .notStarted
        self.hypothesis = ""
        self.observations = []
        self.results = ""
        self.conclusion = ""
        self.reflection = ""
        self.photos = []
        self.videos = []
        self.labPartner = nil
        self.teacherFeedback = nil
        self.grade = nil
    }
}

enum ExperimentStatus: String, CaseIterable, Codable {
    case notStarted = "not_started"
    case inProgress = "in_progress"
    case completed = "completed"
    case needsRevision = "needs_revision"
    case submitted = "submitted"
    case graded = "graded"
    
    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .needsRevision: return "Needs Revision"
        case .submitted: return "Submitted"
        case .graded: return "Graded"
        }
    }
}

struct StudentObservation: Identifiable, Codable {
    let id: UUID
    let observationPointId: UUID
    let timestamp: Date
    let value: String
    let notes: String
    let mediaURLs: [String]
    
    init(observationPointId: UUID, value: String, notes: String = "") {
        self.id = UUID()
        self.observationPointId = observationPointId
        self.timestamp = Date()
        self.value = value
        self.notes = notes
        self.mediaURLs = []
    }
}
