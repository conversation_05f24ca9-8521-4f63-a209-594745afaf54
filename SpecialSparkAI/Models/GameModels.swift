//
//  GameModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Achievement System

struct GameAchievement: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let icon: String
    let category: AchievementCategory
    let points: Int
    let rarity: AchievementRarity
    let unlockedAt: Date?
    let progress: Double // 0.0 to 1.0
    let maxProgress: Int
    let currentProgress: Int
    let requirements: [String]
    let rewards: [AchievementReward]

    var isUnlocked: Bool {
        return unlockedAt != nil
    }

    var progressPercentage: Int {
        return Int(progress * 100)
    }
}

enum AchievementCategory: String, CaseIterable, Codable {
    case learning = "Learning"
    case social = "Social"
    case creativity = "Creativity"
    case persistence = "Persistence"
    case exploration = "Exploration"
    case kindness = "Kindness"
    case leadership = "Leadership"
    case innovation = "Innovation"

    var icon: String {
        switch self {
        case .learning: return "brain.head.profile"
        case .social: return "person.2.fill"
        case .creativity: return "paintbrush.fill"
        case .persistence: return "target"
        case .exploration: return "safari.fill"
        case .kindness: return "heart.fill"
        case .leadership: return "crown.fill"
        case .innovation: return "lightbulb.fill"
        }
    }

    var color: Color {
        switch self {
        case .learning: return .blue
        case .social: return .green
        case .creativity: return .purple
        case .persistence: return .orange
        case .exploration: return .teal
        case .kindness: return .pink
        case .leadership: return .yellow
        case .innovation: return .indigo
        }
    }
}

enum AchievementRarity: String, CaseIterable, Codable {
    case common = "Common"
    case uncommon = "Uncommon"
    case rare = "Rare"
    case epic = "Epic"
    case legendary = "Legendary"

    var color: Color {
        switch self {
        case .common: return .gray
        case .uncommon: return .green
        case .rare: return .blue
        case .epic: return .purple
        case .legendary: return .orange
        }
    }

    var points: Int {
        switch self {
        case .common: return 10
        case .uncommon: return 25
        case .rare: return 50
        case .epic: return 100
        case .legendary: return 250
        }
    }
}

struct AchievementReward: Identifiable, Codable {
    let id: UUID
    let type: RewardType
    let value: String
    let description: String
}

enum RewardType: String, CaseIterable, Codable {
    case badge = "Badge"
    case points = "Points"
    case avatar = "Avatar"
    case theme = "Theme"
    case feature = "Feature"
    case certificate = "Certificate"
}

// MARK: - Badge System

struct Badge: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let icon: String
    let color: String
    let category: BadgeCategory
    let earnedAt: Date?
    let requirements: [String]

    var isEarned: Bool {
        return earnedAt != nil
    }
}

enum BadgeCategory: String, CaseIterable, Codable {
    case academic = "Academic"
    case behavioral = "Behavioral"
    case social = "Social"
    case creative = "Creative"
    case special = "Special"

    var color: Color {
        switch self {
        case .academic: return .blue
        case .behavioral: return .green
        case .social: return .orange
        case .creative: return .purple
        case .special: return .gold
        }
    }
}

// MARK: - Leaderboard System

struct LeaderboardEntry: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let studentName: String
    let points: Int
    let rank: Int
    let category: LeaderboardCategory
    let period: LeaderboardPeriod
    let lastUpdated: Date
}

enum LeaderboardCategory: String, CaseIterable, Codable {
    case overall = "Overall"
    case mathematics = "Mathematics"
    case reading = "Reading"
    case science = "Science"
    case creativity = "Creativity"
    case kindness = "Kindness"
    case participation = "Participation"
}

enum LeaderboardPeriod: String, CaseIterable, Codable {
    case daily = "Daily"
    case weekly = "Weekly"
    case monthly = "Monthly"
    case allTime = "All Time"
}

// MARK: - Reward System

struct StudentReward: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let type: RewardType
    let title: String
    let description: String
    let value: String
    let earnedAt: Date
    let isRedeemed: Bool
    let redeemedAt: Date?
}

// MARK: - Progress Tracking

struct LearningStreak: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let subject: String
    let currentStreak: Int
    let longestStreak: Int
    let lastActivityDate: Date
    let streakType: StreakType
}

enum StreakType: String, CaseIterable, Codable {
    case daily = "Daily"
    case weekly = "Weekly"
    case monthly = "Monthly"

    var icon: String {
        switch self {
        case .daily: return "calendar"
        case .weekly: return "calendar.badge.plus"
        case .monthly: return "calendar.badge.clock"
        }
    }
}

// MARK: - Social Features

struct StudentConnection: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let friendId: UUID
    let friendName: String
    let connectionType: ConnectionType
    let connectedAt: Date
    let isActive: Bool
    let sharedActivities: [String]
}

enum ConnectionType: String, CaseIterable, Codable {
    case classmate = "Classmate"
    case studyBuddy = "Study Buddy"
    case projectPartner = "Project Partner"
    case mentor = "Mentor"
    case mentee = "Mentee"
}

// MARK: - Parent Portal Models

struct ParentDashboardData: Codable {
    let studentProgress: StudentProgressSummary
    let recentAchievements: [GameAchievement]
    let upcomingActivities: [ScheduledActivity]
    let teacherNotes: [TeacherNote]
    let behaviorReports: [BehaviorReport]
    let assessmentResults: [AssessmentSummary]
}

struct StudentProgressSummary: Codable {
    let studentId: UUID
    let overallGrade: String
    let subjectGrades: [String: String]
    let attendanceRate: Double
    let engagementScore: Double
    let improvementAreas: [String]
    let strengths: [String]
    let lastUpdated: Date
}

struct ScheduledActivity: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let subject: String
    let scheduledDate: Date
    let duration: Int
    let type: ActivityType
    let isRequired: Bool
}

// ActivityType is defined in AITeacherModels.swift to avoid conflicts

struct TeacherNote: Identifiable, Codable {
    let id: UUID
    let teacherId: UUID
    let teacherName: String
    let studentId: UUID
    let subject: String
    let note: String
    let category: NoteCategory
    let isPrivate: Bool
    let createdAt: Date
}

enum NoteCategory: String, CaseIterable, Codable {
    case academic = "Academic"
    case behavioral = "Behavioral"
    case social = "Social"
    case achievement = "Achievement"
    case concern = "Concern"
    case celebration = "Celebration"
}

struct BehaviorReport: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let date: Date
    let behavior: String
    let context: String
    let intervention: String?
    let outcome: String?
    let reportedBy: String
    let severity: BehaviorSeverity
}

enum BehaviorSeverity: String, CaseIterable, Codable {
    case positive = "Positive"
    case neutral = "Neutral"
    case minor = "Minor Concern"
    case moderate = "Moderate Concern"
    case major = "Major Concern"

    var color: Color {
        switch self {
        case .positive: return .green
        case .neutral: return .blue
        case .minor: return .yellow
        case .moderate: return .orange
        case .major: return .red
        }
    }
}

struct AssessmentSummary: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let subject: String
    let assessmentName: String
    let score: Double
    let maxScore: Double
    let grade: String
    let completedAt: Date
    let timeSpent: Int // minutes
    let strengths: [String]
    let improvementAreas: [String]
}

// MARK: - Campus Models

struct CampusBuilding: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let icon: String
    let color: String
    let capacity: Int
    let currentOccupancy: Int
    let buildingType: BuildingType
    let features: [String]
    let accessibilityFeatures: [String]
    let isOpen: Bool
    let openingHours: String
}

// MARK: - Extensions

extension Color {
    static let gold = Color(red: 1.0, green: 0.84, blue: 0.0)
}
