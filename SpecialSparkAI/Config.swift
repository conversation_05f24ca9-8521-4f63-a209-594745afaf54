import Foundation

// MARK: - API Configuration
struct APIConfig {

    // MARK: - Gemini AI Configuration
    struct Gemini {
        // TODO: Add your Gemini API key here
        // Get your API key from: https://makersuite.google.com/app/apikey
        static let apiKey = "AIzaSyCBbWYAE8fiwup1Jcv22c0DrEBzurVAcYM"
        static let baseURL = "https://generativelanguage.googleapis.com/v1beta"
        static let model = "gemini-2.0-flash"

        // AI Configuration
        static let maxTokens = 1024
        static let temperature = 0.7
        static let topK = 40
        static let topP = 0.95
        static let timeout: TimeInterval = 30.0

        // Safety Settings
        static let safetyLevel = "BLOCK_MEDIUM_AND_ABOVE"
        static let maxConversationHistory = 50
        static let maxMessageLength = 500
    }

    // MARK: - Supabase Configuration
    struct Supabase {
        // TODO: Add your Supabase configuration here
        // Get these from your Supabase project dashboard
        static let url = "https://mxcfcazeqirszfpyjdvh.supabase.co"
        static let anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im14Y2ZjYXplcWlyc3pmcHlqZHZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwOTcwODAsImV4cCI6MjA2MzY3MzA4MH0.Zs7XS1E3Yxr0wkNg3uXnf2WwNC9EOyiz3Sp7jQxrJU4"
        static let serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im14Y2ZjYXplcWlyc3pmcHlqZHZoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA5NzA4MCwiZXhwIjoyMDYzNjczMDgwfQ.3t7SIPUOgzpH6Kzc1OU3cCTZzV5_KJzW5vB7QGLGxsk"
    }

    // MARK: - Legacy API Config (for compatibility)
    struct APIConfig {
        static let geminiAPIKey = Gemini.apiKey
        static let supabaseURL = Supabase.url
        static let supabaseAnonKey = Supabase.anonKey
    }

    // MARK: - Development Configuration
    struct Development {
        static let useMockData = true // Set to false when using real APIs
        static let enableLogging = true
        static let debugMode = true
    }

    // MARK: - Validation
    static var isConfigured: Bool {
        return !Gemini.apiKey.contains("YOUR_") &&
               !Supabase.url.contains("YOUR_") &&
               !Supabase.anonKey.contains("YOUR_")
    }

    static func validateConfiguration() {
        if !isConfigured {
            print("⚠️ WARNING: API keys not configured!")
            print("📝 Please update Config.swift with your API keys:")
            print("   1. Gemini API Key: https://makersuite.google.com/app/apikey")
            print("   2. Supabase URL & Keys: https://supabase.com/dashboard")
        }
    }
}

// MARK: - Environment Helper
extension APIConfig {
    static func loadFromEnvironment() {
        // This allows loading from environment variables in production
        if ProcessInfo.processInfo.environment["GEMINI_API_KEY"] != nil {
            // In a real app, you'd use a more secure method to store this
            print("✅ Gemini API key loaded from environment")
        }

        if ProcessInfo.processInfo.environment["SUPABASE_URL"] != nil {
            print("✅ Supabase URL loaded from environment")
        }
    }
}
