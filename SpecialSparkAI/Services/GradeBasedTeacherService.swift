import Foundation
import SwiftData

// MARK: - Grade-Based Teacher Service
// Generates appropriate AI teachers based on student's grade level

class GradeBasedTeacherService {
    static let shared = GradeBasedTeacherService()

    private init() {}

    // MARK: - Get Teachers for Student

    func getTeachersFor(student: Student) -> [AIAgent] {
        return getTeachersFor(gradeLevel: student.gradeLevel, specialNeeds: student.specialNeeds)
    }

    func getTeachersFor(gradeLevel: GradeLevel, specialNeeds: [SpecialNeedsType] = [.none]) -> [AIAgent] {
        let subjects = SubjectDataService.shared.getSubjectsFor(gradeLevel: gradeLevel)
        var teachers: [AIAgent] = []

        // Create subject specialist teachers for each available subject
        for subject in subjects {
            let teacher = createSubjectTeacher(for: subject, gradeLevel: gradeLevel, specialNeeds: specialNeeds)
            teachers.append(teacher)
        }

        // Add general support teachers (available for all grades)
        teachers.append(contentsOf: createSupportTeachers(for: gradeLevel, specialNeeds: specialNeeds))

        return teachers
    }

    // MARK: - Create Subject-Specific Teachers

    private func createSubjectTeacher(for subject: Subject, gradeLevel: GradeLevel, specialNeeds: [SpecialNeedsType]) -> AIAgent {
        let teacherName = generateTeacherName(for: subject, gradeLevel: gradeLevel)
        let gradeLevels = subject.gradeLevels
        let schoolLevels = subject.schoolLevels

        let teacher = AIAgent(
            name: teacherName,
            agentType: .subjectSpecialist,
            specialization: subject.name,
            gradeLevels: gradeLevels.map { $0.rawValue },
            schoolLevels: schoolLevels.map { $0.rawValue }
        )

        teacher.subjectId = subject.id
        teacher.bio = generateTeacherBio(for: subject, gradeLevel: gradeLevel)
        teacher.avatarURL = generateAvatarURL(for: subject)
        teacher.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }

        // Customize personality based on subject and grade level
        customizePersonality(teacher: teacher, subject: subject, gradeLevel: gradeLevel)

        return teacher
    }

    // MARK: - Create Support Teachers

    private func createSupportTeachers(for gradeLevel: GradeLevel, specialNeeds: [SpecialNeedsType]) -> [AIAgent] {
        var supportTeachers: [AIAgent] = []

        // Learning Coach
        let learningCoach = AIAgent(
            name: "Learning Coach",
            agentType: .learningCoach,
            specialization: "Personalized Learning Guidance",
            gradeLevels: [gradeLevel.rawValue],
            schoolLevels: [gradeLevel.schoolLevel.rawValue]
        )
        learningCoach.bio = "I help students develop effective learning strategies and stay motivated on their educational journey."
        learningCoach.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }
        supportTeachers.append(learningCoach)

        // Emotional Support (especially important for special needs)
        if specialNeeds.contains(where: { $0 != .none }) {
            let emotionalSupport = AIAgent(
                name: "Counselor",
                agentType: .emotionalSupport,
                specialization: "Emotional Well-being & Support",
                gradeLevels: [gradeLevel.rawValue],
                schoolLevels: [gradeLevel.schoolLevel.rawValue]
            )
            emotionalSupport.bio = "I provide emotional support and help students build confidence and resilience."
            emotionalSupport.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }
            supportTeachers.append(emotionalSupport)
        }

        // Assessment Agent
        let assessmentAgent = AIAgent(
            name: "Assessment Specialist",
            agentType: .assessmentAgent,
            specialization: "Adaptive Assessment & Progress Tracking",
            gradeLevels: [gradeLevel.rawValue],
            schoolLevels: [gradeLevel.schoolLevel.rawValue]
        )
        assessmentAgent.bio = "I create personalized assessments and track your learning progress to ensure continuous improvement."
        assessmentAgent.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }
        supportTeachers.append(assessmentAgent)

        // Parent Communicator
        let parentCommunicator = AIAgent(
            name: "Parent Liaison",
            agentType: .parentCommunicator,
            specialization: "Parent-Student-School Communication",
            gradeLevels: [gradeLevel.rawValue],
            schoolLevels: [gradeLevel.schoolLevel.rawValue]
        )
        parentCommunicator.bio = "I keep parents informed about their child's progress and facilitate communication between home and school."
        parentCommunicator.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }
        supportTeachers.append(parentCommunicator)

        // Creative Mentor (for arts and creativity)
        let creativeMentor = AIAgent(
            name: "Art Teacher",
            agentType: .creativeMentor,
            specialization: "Creative Expression & Arts",
            gradeLevels: [gradeLevel.rawValue],
            schoolLevels: [gradeLevel.schoolLevel.rawValue]
        )
        creativeMentor.bio = "I inspire creativity and help students express themselves through various art forms and creative projects."
        creativeMentor.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }
        supportTeachers.append(creativeMentor)

        // Social Skills Coach (especially for special needs)
        if specialNeeds.contains(where: { $0 == .autism || $0 == .emotionalBehavioral || $0 == .intellectualDisability }) {
            let socialSkillsCoach = AIAgent(
                name: "Social Skills Coach",
                agentType: .socialSkillsCoach,
                specialization: "Social Skills & Communication",
                gradeLevels: [gradeLevel.rawValue],
                schoolLevels: [gradeLevel.schoolLevel.rawValue]
            )
            socialSkillsCoach.bio = "I help students develop social skills, communication abilities, and build meaningful relationships."
            socialSkillsCoach.specialNeedsAdaptations = specialNeeds.map { $0.rawValue }
            supportTeachers.append(socialSkillsCoach)
        }

        return supportTeachers
    }

    // MARK: - Helper Methods

    private func generateTeacherName(for subject: Subject, gradeLevel: GradeLevel) -> String {
        // Use role-based names instead of personal names
        if subject.isAP {
            return "\(subject.name) AP Teacher"
        }

        return "\(subject.name) Teacher"
    }



    private func generateTeacherBio(for subject: Subject, gradeLevel: GradeLevel) -> String {
        let schoolLevelText = gradeLevel.schoolLevel.displayName.lowercased()

        if subject.isAP {
            return "I'm an expert in \(subject.name) with extensive experience preparing students for AP exams. I make complex concepts accessible and help students achieve their highest potential."
        }

        switch subject.category {
        case .core:
            return "I specialize in teaching \(subject.name) to \(schoolLevelText) students. I believe every student can master these essential skills with the right support and encouragement."
        case .stem:
            return "I'm passionate about \(subject.name) and love helping students discover the wonders of science and mathematics. I make learning interactive and fun!"
        case .language:
            return "¡Hola! I'm excited to help you learn \(subject.name). I believe language learning opens doors to new cultures and opportunities."
        case .arts:
            return "I'm here to help you explore your creativity through \(subject.name). Art is a wonderful way to express yourself and see the world differently."
        case .specialNeeds:
            return "I specialize in \(subject.name) and am dedicated to helping every student reach their full potential with personalized support and understanding."
        default:
            return "I'm passionate about \(subject.name) and committed to helping you succeed in your learning journey."
        }
    }

    private func generateAvatarURL(for subject: Subject) -> String {
        // In a real app, these would be actual avatar URLs
        let baseURL = "https://api.dicebear.com/7.x/avataaars/svg?seed="
        let seed = subject.code.lowercased()
        return "\(baseURL)\(seed)"
    }

    private func customizePersonality(teacher: AIAgent, subject: Subject, gradeLevel: GradeLevel) {
        // TODO: Implement personality customization with new model structure
        // The AIAgent model now uses personalityId instead of direct personality object
        // This function needs to be updated to work with the new model structure

        // For now, we'll just set a placeholder personalityId
        // In a full implementation, we would create or find an appropriate AgentPersonality
        // and set teacher.personalityId = personality.id
    }
}
