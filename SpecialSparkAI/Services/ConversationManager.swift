//
//  ConversationManager.swift
//  SpecialSparkAI
//
//  Conversation Management and History System
//

import Foundation
import SwiftData

// MARK: - Conversation Models
@Model
class Conversation: Identifiable, Codable {
    @Attribute(.unique) var id: UUID
    var studentId: UUID
    var teacherId: UUID
    var subject: String
    var title: String
    var messages: [ConversationMessage]
    var startedAt: Date
    var lastMessageAt: Date
    var isActive: Bool
    var learningObjectives: [String]
    var assessmentNotes: String

    init(studentId: UUID, teacherId: UUID, subject: String, title: String = "") {
        self.id = UUID()
        self.studentId = studentId
        self.teacherId = teacherId
        self.subject = subject
        self.title = title.isEmpty ? "Conversation with \(subject) Teacher" : title
        self.messages = []
        self.startedAt = Date()
        self.lastMessageAt = Date()
        self.isActive = true
        self.learningObjectives = []
        self.assessmentNotes = ""
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, studentId, teacherId, subject, title, messages
        case startedAt, lastMessageAt, isActive, learningObjectives, assessmentNotes
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.studentId = try container.decode(UUID.self, forKey: .studentId)
        self.teacherId = try container.decode(UUID.self, forKey: .teacherId)
        self.subject = try container.decode(String.self, forKey: .subject)
        self.title = try container.decode(String.self, forKey: .title)
        self.messages = try container.decode([ConversationMessage].self, forKey: .messages)
        self.startedAt = try container.decode(Date.self, forKey: .startedAt)
        self.lastMessageAt = try container.decode(Date.self, forKey: .lastMessageAt)
        self.isActive = try container.decode(Bool.self, forKey: .isActive)
        self.learningObjectives = try container.decode([String].self, forKey: .learningObjectives)
        self.assessmentNotes = try container.decode(String.self, forKey: .assessmentNotes)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(studentId, forKey: .studentId)
        try container.encode(teacherId, forKey: .teacherId)
        try container.encode(subject, forKey: .subject)
        try container.encode(title, forKey: .title)
        try container.encode(messages, forKey: .messages)
        try container.encode(startedAt, forKey: .startedAt)
        try container.encode(lastMessageAt, forKey: .lastMessageAt)
        try container.encode(isActive, forKey: .isActive)
        try container.encode(learningObjectives, forKey: .learningObjectives)
        try container.encode(assessmentNotes, forKey: .assessmentNotes)
    }
}

struct ConversationMessage: Identifiable, Codable {
    var id = UUID()
    let content: String
    let isFromUser: Bool
    var timestamp = Date()
    let messageType: MessageType
    let metadata: MessageMetadata?

    init(content: String, isFromUser: Bool, messageType: MessageType = .text, metadata: MessageMetadata? = nil) {
        self.content = content
        self.isFromUser = isFromUser
        self.messageType = messageType
        self.metadata = metadata
    }

    enum MessageType: String, Codable, CaseIterable {
        case text = "text"
        case question = "question"
        case answer = "answer"
        case encouragement = "encouragement"
        case assessment = "assessment"
        case systemMessage = "system"
    }

    struct MessageMetadata: Codable {
        let confidence: Double?
        let learningObjective: String?
        let assessmentScore: Double?
        let emotionalTone: String?
        let difficulty: String?
    }
}

// MARK: - Conversation Manager
class ConversationManager: ObservableObject {
    static let shared = ConversationManager()

    @Published var activeConversations: [Conversation] = []
    @Published var agentConversations: [UUID: Conversation] = [:] // Agent ID -> Conversation
    @Published var currentConversation: Conversation?
    @Published var isLoading = false

    private let geminiService = GeminiService.shared
    private let supabaseService = SupabaseService.shared
    private let gradeBasedTeacherService = GradeBasedTeacherService.shared
    private let crewAIService = CrewAIService.shared

    private init() {
        loadStoredConversations()
    }

    // MARK: - Conversation Management
    func startConversation(
        student: Student,
        teacher: AITeacherPersonality,
        initialMessage: String? = nil
    ) async -> Conversation {

        let conversation = Conversation(
            studentId: student.id,
            teacherId: teacher.id,
            subject: teacher.subject,
            title: "Learning \(teacher.subject) with \(teacher.name)"
        )

        // Add initial teacher greeting
        let greeting = initialMessage ?? teacher.getRandomConversationStarter()
        let teacherMessage = ConversationMessage(
            content: greeting,
            isFromUser: false,
            messageType: .text
        )

        conversation.messages.append(teacherMessage)
        conversation.lastMessageAt = Date()

        await MainActor.run {
            self.activeConversations.append(conversation)
            self.currentConversation = conversation
        }

        // Save to Supabase if not using mock data
        if !APIConfig.Development.useMockData {
            await saveConversation(conversation)
        }

        return conversation
    }

    func sendMessage(
        _ content: String,
        to conversation: Conversation,
        student: Student,
        teacher: AITeacherPersonality
    ) async throws -> ConversationMessage {

        await MainActor.run {
            self.isLoading = true
        }

        defer {
            Task {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }

        // Add user message
        let userMessage = ConversationMessage(
            content: content,
            isFromUser: true,
            messageType: .text
        )

        conversation.messages.append(userMessage)
        conversation.lastMessageAt = Date()

        // Generate AI response
        let systemPrompt = teacher.getPersonalizedSystemPrompt(for: student)
        let _ = Array(conversation.messages.suffix(APIConfig.Gemini.maxConversationHistory))

        let aiResponse = await geminiService.generateResponse(
            prompt: content,
            context: systemPrompt,
            personality: teacher.name
        )

        // Create AI message
        let aiMessage = ConversationMessage(
            content: aiResponse,
            isFromUser: false,
            messageType: .answer,
            metadata: ConversationMessage.MessageMetadata(
                confidence: 0.9,
                learningObjective: nil,
                assessmentScore: nil,
                emotionalTone: "encouraging",
                difficulty: "appropriate"
            )
        )

        conversation.messages.append(aiMessage)
        conversation.lastMessageAt = Date()

        // Update UI
        await MainActor.run {
            if let index = self.activeConversations.firstIndex(where: { $0.id == conversation.id }) {
                self.activeConversations[index] = conversation
            }
            self.currentConversation = conversation
        }

        // Save to Supabase
        if !APIConfig.Development.useMockData {
            await saveConversation(conversation)
        }

        return aiMessage
    }

    // MARK: - AI Agent Conversations
    func startConversationWithAgent(
        student: Student,
        agent: AIAgent,
        initialMessage: String? = nil
    ) async -> Conversation {

        // Check if conversation already exists for this agent
        if let existingConversation = agentConversations[agent.id] {
            await MainActor.run {
                self.currentConversation = existingConversation
            }
            return existingConversation
        }

        let conversation = Conversation(
            studentId: student.id,
            teacherId: agent.id,
            subject: agent.specialization,
            title: "Chat with \(agent.name)"
        )

        // Add initial agent greeting
        let greeting = initialMessage ?? agent.getPersonalizedGreeting(for: student.firstName)
        let agentMessage = ConversationMessage(
            content: greeting,
            isFromUser: false,
            messageType: .text
        )

        conversation.messages.append(agentMessage)
        conversation.lastMessageAt = Date()

        await MainActor.run {
            self.agentConversations[agent.id] = conversation
            self.activeConversations.append(conversation)
            self.currentConversation = conversation
        }

        // Save to local storage
        saveAgentConversations()

        return conversation
    }

    func sendMessageToAgent(
        _ content: String,
        to conversation: Conversation,
        student: Student,
        agent: AIAgent
    ) async throws -> ConversationMessage {

        await MainActor.run {
            self.isLoading = true
        }

        defer {
            Task {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }

        // Add user message
        let userMessage = ConversationMessage(
            content: content,
            isFromUser: true,
            messageType: .text
        )

        conversation.messages.append(userMessage)
        conversation.lastMessageAt = Date()

        // Generate AI response using agent's personality and context
        let systemPrompt = agent.getPersonalizedSystemPrompt(
            for: student.firstName,
            gradeLevel: student.gradeLevel.displayName,
            schoolLevel: student.schoolLevel.displayName,
            specialNeeds: student.specialNeeds.map { $0.rawValue }
        )
        let conversationHistory = Array(conversation.messages.suffix(APIConfig.Gemini.maxConversationHistory))

        // Build context from conversation history
        let historyContext = conversationHistory.map { message in
            let role = message.isFromUser ? "Student" : agent.name
            return "\(role): \(message.content)"
        }.joined(separator: "\n")

        let fullContext = """
        \(systemPrompt)

        Previous conversation:
        \(historyContext)

        Current student message: \(content)

        Respond as \(agent.name), keeping in mind:
        - Your specialization: \(agent.specialization)
        - Student's grade level: \(student.gradeLevel.displayName)
        - Student's special needs: \(student.specialNeeds.map { $0.displayName }.joined(separator: ", "))
        - Be encouraging, patient, and adaptive to their learning style
        """

        let aiResponse = await geminiService.generateResponse(
            prompt: content,
            context: fullContext,
            personality: agent.name
        )

        // Create AI message
        let aiMessage = ConversationMessage(
            content: aiResponse,
            isFromUser: false,
            messageType: .answer,
            metadata: ConversationMessage.MessageMetadata(
                confidence: 0.9,
                learningObjective: nil,
                assessmentScore: nil,
                emotionalTone: "encouraging",
                difficulty: "appropriate"
            )
        )

        conversation.messages.append(aiMessage)
        conversation.lastMessageAt = Date()

        // Update UI and storage
        await MainActor.run {
            self.agentConversations[agent.id] = conversation
            if let index = self.activeConversations.firstIndex(where: { $0.id == conversation.id }) {
                self.activeConversations[index] = conversation
            }
            self.currentConversation = conversation
        }

        // Save to local storage
        saveAgentConversations()

        // Report to Principal Agent (CrewAI collaboration)
        await reportInteractionToPrincipal(
            agent: agent,
            student: student,
            userMessage: content,
            agentResponse: aiResponse,
            conversation: conversation
        )

        return aiMessage
    }

    func endConversation(_ conversation: Conversation) async {
        conversation.isActive = false
        conversation.lastMessageAt = Date()

        await MainActor.run {
            if let index = self.activeConversations.firstIndex(where: { $0.id == conversation.id }) {
                self.activeConversations[index] = conversation
            }

            if self.currentConversation?.id == conversation.id {
                self.currentConversation = nil
            }
        }

        // Save final state
        if !APIConfig.Development.useMockData {
            await saveConversation(conversation)
        }
    }

    // MARK: - Data Persistence
    private func saveConversation(_ conversation: Conversation) async {
        do {
            try await supabaseService.saveConversation(conversation)
        } catch {
            print("Failed to save conversation: \(error)")
        }
    }

    func loadConversations(for studentId: UUID) async {
        do {
            let conversations = try await supabaseService.getConversations(for: studentId)
            await MainActor.run {
                self.activeConversations = conversations
            }
        } catch {
            print("Failed to load conversations: \(error)")
        }
    }

    // MARK: - Conversation Analysis
    func analyzeConversation(_ conversation: Conversation) -> ConversationAnalysis {
        let totalMessages = conversation.messages.count
        let userMessages = conversation.messages.filter { $0.isFromUser }
        let aiMessages = conversation.messages.filter { !$0.isFromUser }

        let duration = conversation.lastMessageAt.timeIntervalSince(conversation.startedAt)
        let averageResponseTime = duration / Double(max(userMessages.count, 1))

        let engagementScore = calculateEngagementScore(conversation)
        let learningProgress = assessLearningProgress(conversation)

        return ConversationAnalysis(
            totalMessages: totalMessages,
            userMessages: userMessages.count,
            aiMessages: aiMessages.count,
            duration: duration,
            averageResponseTime: averageResponseTime,
            engagementScore: engagementScore,
            learningProgress: learningProgress,
            keyTopics: extractKeyTopics(conversation),
            recommendations: generateRecommendations(conversation)
        )
    }

    private func calculateEngagementScore(_ conversation: Conversation) -> Double {
        let messageCount = conversation.messages.filter { $0.isFromUser }.count
        let duration = conversation.lastMessageAt.timeIntervalSince(conversation.startedAt)

        // Simple engagement calculation based on message frequency
        let messagesPerMinute = Double(messageCount) / (duration / 60.0)
        return min(messagesPerMinute * 10, 100) // Scale to 0-100
    }

    private func assessLearningProgress(_ conversation: Conversation) -> Double {
        // Simple progress assessment based on conversation length and complexity
        let messageCount = conversation.messages.filter { $0.isFromUser }.count
        return min(Double(messageCount) * 5, 100) // Scale to 0-100
    }

    private func extractKeyTopics(_ conversation: Conversation) -> [String] {
        // Simple keyword extraction from user messages
        let userMessages = conversation.messages.filter { $0.isFromUser }
        let allText = userMessages.map { $0.content }.joined(separator: " ")

        // Basic topic extraction (in a real app, use NLP)
        let commonWords = ["math", "science", "reading", "problem", "question", "help", "learn"]
        return commonWords.filter { allText.lowercased().contains($0) }
    }

    private func generateRecommendations(_ conversation: Conversation) -> [String] {
        let analysis = analyzeConversation(conversation)
        var recommendations: [String] = []

        if analysis.engagementScore < 30 {
            recommendations.append("Try more interactive activities to increase engagement")
        }

        if analysis.userMessages < 5 {
            recommendations.append("Encourage more student participation")
        }

        if analysis.duration < 300 { // Less than 5 minutes
            recommendations.append("Consider longer learning sessions")
        }

        return recommendations
    }

    // MARK: - Local Storage for Agent Conversations
    private func saveAgentConversations() {
        do {
            let data = try JSONEncoder().encode(agentConversations)
            UserDefaults.standard.set(data, forKey: "agentConversations")
        } catch {
            print("Failed to save agent conversations: \(error)")
        }
    }

    private func loadStoredConversations() {
        guard let data = UserDefaults.standard.data(forKey: "agentConversations") else { return }

        do {
            let conversations = try JSONDecoder().decode([UUID: Conversation].self, from: data)
            agentConversations = conversations
            activeConversations.append(contentsOf: conversations.values)
        } catch {
            print("Failed to load agent conversations: \(error)")
        }
    }

    func clearAllConversations() {
        agentConversations.removeAll()
        activeConversations.removeAll()
        currentConversation = nil
        UserDefaults.standard.removeObject(forKey: "agentConversations")
    }

    func getConversationForAgent(_ agentId: UUID) -> Conversation? {
        return agentConversations[agentId]
    }

    func hasConversationWithAgent(_ agentId: UUID) -> Bool {
        return agentConversations[agentId] != nil
    }

    // MARK: - CrewAI Integration

    private func reportInteractionToPrincipal(
        agent: AIAgent,
        student: Student,
        userMessage: String,
        agentResponse: String,
        conversation: Conversation
    ) async {
        // Analyze the interaction to determine if principal should be notified
        let shouldReport = shouldReportToPrincipal(
            userMessage: userMessage,
            agentResponse: agentResponse,
            conversation: conversation
        )

        guard shouldReport.shouldReport else { return }

        let report = generateInteractionReport(
            agent: agent,
            student: student,
            userMessage: userMessage,
            agentResponse: agentResponse,
            conversation: conversation,
            priority: shouldReport.priority
        )

        await crewAIService.reportToPrincipal(
            from: agent,
            about: student,
            report: report,
            priority: shouldReport.priority
        )
    }

    private func shouldReportToPrincipal(
        userMessage: String,
        agentResponse: String,
        conversation: Conversation
    ) -> (shouldReport: Bool, priority: ReportPriority) {

        let urgentKeywords = ["help", "confused", "don't understand", "frustrated", "angry", "sad", "bullying"]
        let concernKeywords = ["struggling", "difficult", "hard", "can't do", "give up"]
        let positiveKeywords = ["great", "excellent", "love", "fun", "easy", "understand"]

        let messageText = userMessage.lowercased()

        // Check for urgent situations
        if urgentKeywords.contains(where: { messageText.contains($0) }) {
            return (true, .urgent)
        }

        // Check for concerns
        if concernKeywords.contains(where: { messageText.contains($0) }) {
            return (true, .high)
        }

        // Report positive achievements
        if positiveKeywords.contains(where: { messageText.contains($0) }) && conversation.messages.count > 10 {
            return (true, .normal)
        }

        // Report every 20 messages for regular check-ins
        if conversation.messages.count % 20 == 0 {
            return (true, .low)
        }

        return (false, .normal)
    }

    private func generateInteractionReport(
        agent: AIAgent,
        student: Student,
        userMessage: String,
        agentResponse: String,
        conversation: Conversation,
        priority: ReportPriority
    ) -> String {

        let messageCount = conversation.messages.count
        let duration = conversation.lastMessageAt.timeIntervalSince(conversation.startedAt)
        let durationMinutes = Int(duration / 60)

        return """
        📋 STUDENT INTERACTION REPORT

        👨‍🎓 Student: \(student.firstName) \(student.lastName)
        👩‍🏫 Teacher: \(agent.name) (\(agent.specialization))
        📚 Subject: \(conversation.subject)
        ⏱️ Session Duration: \(durationMinutes) minutes
        💬 Total Messages: \(messageCount)

        📝 Latest Interaction:
        Student said: "\(userMessage)"
        I responded: "\(agentResponse)"

        📊 Session Summary:
        - Student engagement: \(assessEngagement(conversation: conversation))
        - Learning progress: \(assessProgress(conversation: conversation))
        - Areas of focus: \(conversation.subject)

        🎯 Recommendations:
        \(generateTeacherRecommendations(conversation: conversation, priority: priority))

        ---
        Report generated by \(agent.role) at \(Date().formatted(date: .abbreviated, time: .shortened))
        """
    }

    private func assessEngagement(conversation: Conversation) -> String {
        let userMessages = conversation.messages.filter { $0.isFromUser }.count
        let totalMessages = conversation.messages.count
        let engagementRatio = Double(userMessages) / Double(max(totalMessages, 1))

        if engagementRatio > 0.6 {
            return "High - Student is actively participating"
        } else if engagementRatio > 0.4 {
            return "Moderate - Student is somewhat engaged"
        } else {
            return "Low - Student needs more encouragement"
        }
    }

    private func assessProgress(conversation: Conversation) -> String {
        let messageCount = conversation.messages.filter { $0.isFromUser }.count

        if messageCount > 15 {
            return "Excellent - Extended learning session"
        } else if messageCount > 8 {
            return "Good - Sustained interaction"
        } else if messageCount > 3 {
            return "Fair - Basic interaction"
        } else {
            return "Needs attention - Limited interaction"
        }
    }

    private func generateTeacherRecommendations(
        conversation: Conversation,
        priority: ReportPriority
    ) -> String {
        switch priority {
        case .urgent:
            return "• Immediate intervention may be needed\n• Consider one-on-one support\n• Alert parents if necessary"
        case .high:
            return "• Monitor closely in next session\n• Provide additional scaffolding\n• Consider peer support"
        case .normal:
            return "• Continue current approach\n• Celebrate achievements\n• Maintain engagement"
        case .low:
            return "• Regular progress check\n• Continue monitoring\n• Document for records"
        }
    }
}

// MARK: - Analysis Models
struct ConversationAnalysis {
    let totalMessages: Int
    let userMessages: Int
    let aiMessages: Int
    let duration: TimeInterval
    let averageResponseTime: TimeInterval
    let engagementScore: Double
    let learningProgress: Double
    let keyTopics: [String]
    let recommendations: [String]
}

// MARK: - Extensions
extension Conversation {
    var formattedDuration: String {
        let duration = lastMessageAt.timeIntervalSince(startedAt)
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    var messageCount: Int {
        return messages.count
    }

    var userMessageCount: Int {
        return messages.filter { $0.isFromUser }.count
    }

    var lastMessage: ConversationMessage? {
        return messages.last
    }
}
