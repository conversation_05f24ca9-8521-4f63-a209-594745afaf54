//
//  LessonDataService.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

class LessonDataService: ObservableObject {
    @Published var courses: [Course] = []
    @Published var lessons: [Lesson] = []
    @Published var books: [Book] = []
    @Published var experiments: [ScienceExperiment] = []
    @Published var artProjects: [ArtProject] = []

    private let subjectDataService = SubjectDataService()

    init() {
        loadAllContent()
    }

    private func loadAllContent() {
        loadCourses()
        loadLessons()
        loadBooks()
        loadExperiments()
        loadArtProjects()
    }

    // MARK: - Filtering Methods

    func getCoursesForGrade(_ gradeLevel: GradeLevel) -> [Course] {
        return courses.filter { $0.gradeLevel == gradeLevel }
    }

    func getLessonsForSubject(_ subjectId: UUID, gradeLevel: GradeLevel) -> [Lesson] {
        return lessons.filter { $0.subjectId == subjectId && $0.gradeLevel == gradeLevel }
    }

    func getBooksForGrade(_ gradeLevel: GradeLevel) -> [Book] {
        return books.filter { $0.gradeLevel == gradeLevel }
    }

    func getExperimentsForGrade(_ gradeLevel: GradeLevel) -> [ScienceExperiment] {
        return experiments.filter { $0.gradeLevel == gradeLevel }
    }

    func getArtProjectsForGrade(_ gradeLevel: GradeLevel) -> [ArtProject] {
        return artProjects.filter { $0.gradeLevel == gradeLevel }
    }

    func getRecommendedBooks(for gradeLevel: GradeLevel, limit: Int = 10) -> [Book] {
        return books.filter { $0.gradeLevel == gradeLevel && $0.isRecommended }
            .prefix(limit)
            .map { $0 }
    }

    // MARK: - Content Loading

    private func loadCourses() {
        let subjects = subjectDataService.allSubjects

        for subject in subjects {
            for gradeLevel in subject.gradeLevels {
                let course = Course(
                    title: "\(subject.name) - Grade \(gradeLevel.displayName)",
                    description: subject.subjectDescription,
                    subjectId: subject.id,
                    gradeLevel: gradeLevel,
                    category: mapSubjectCategoryToCourseCategory(subject.category)
                )
                courses.append(course)
            }
        }
    }

    private func mapSubjectCategoryToCourseCategory(_ category: SubjectCategory) -> CourseCategory {
        switch category {
        case .core: return .core
        case .ap: return .ap
        case .specialNeeds: return .specialNeeds
        case .elective: return .elective
        default: return .core
        }
    }

    private func loadLessons() {
        // Sample lessons for different subjects and grades
        loadMathLessons()
        loadScienceLessons()
        loadReadingLessons()
        loadSocialStudiesLessons()
    }

    private func loadMathLessons() {
        let mathSubject = subjectDataService.allSubjects.first { $0.name.contains("Mathematics") }
        guard let mathSubjectId = mathSubject?.id else { return }

        // Kindergarten Math
        let countingLesson = Lesson(
            title: "Counting to 10",
            description: "Learn to count from 1 to 10 with fun activities",
            subjectId: mathSubjectId,
            gradeLevel: .kindergarten,
            duration: 30
        )
        lessons.append(countingLesson)

        // 1st Grade Math
        let additionLesson = Lesson(
            title: "Simple Addition",
            description: "Adding numbers up to 10",
            subjectId: mathSubjectId,
            gradeLevel: .grade1,
            duration: 45
        )
        lessons.append(additionLesson)

        // 3rd Grade Math
        let multiplicationLesson = Lesson(
            title: "Introduction to Multiplication",
            description: "Understanding multiplication as repeated addition",
            subjectId: mathSubjectId,
            gradeLevel: .grade3,
            duration: 50
        )
        lessons.append(multiplicationLesson)

        // 5th Grade Math
        let fractionsLesson = Lesson(
            title: "Working with Fractions",
            description: "Adding and subtracting fractions with like denominators",
            subjectId: mathSubjectId,
            gradeLevel: .grade5,
            duration: 60
        )
        lessons.append(fractionsLesson)

        // Middle School Math
        let algebraLesson = Lesson(
            title: "Introduction to Algebra",
            description: "Solving simple algebraic equations",
            subjectId: mathSubjectId,
            gradeLevel: .grade7,
            duration: 55
        )
        lessons.append(algebraLesson)

        // High School Math
        let calculusLesson = Lesson(
            title: "Limits and Continuity",
            description: "Understanding the concept of limits in calculus",
            subjectId: mathSubjectId,
            gradeLevel: .grade11,
            duration: 90
        )
        lessons.append(calculusLesson)
    }

    private func loadScienceLessons() {
        let scienceSubject = subjectDataService.allSubjects.first { $0.name.contains("Science") }
        guard let scienceSubjectId = scienceSubject?.id else { return }

        // Elementary Science
        let plantsLesson = Lesson(
            title: "How Plants Grow",
            description: "Exploring plant life cycles and needs",
            subjectId: scienceSubjectId,
            gradeLevel: .grade2,
            duration: 45
        )
        lessons.append(plantsLesson)

        let weatherLesson = Lesson(
            title: "Weather Patterns",
            description: "Understanding different types of weather",
            subjectId: scienceSubjectId,
            gradeLevel: .grade4,
            duration: 50
        )
        lessons.append(weatherLesson)

        // Middle School Science
        let cellsLesson = Lesson(
            title: "Cell Structure and Function",
            description: "Exploring the basic unit of life",
            subjectId: scienceSubjectId,
            gradeLevel: .grade6,
            duration: 60
        )
        lessons.append(cellsLesson)

        // High School Science
        let chemistryLesson = Lesson(
            title: "Chemical Reactions",
            description: "Understanding how atoms combine and react",
            subjectId: scienceSubjectId,
            gradeLevel: .grade9,
            duration: 90
        )
        lessons.append(chemistryLesson)
    }

    private func loadReadingLessons() {
        let readingSubject = subjectDataService.allSubjects.first { $0.name.contains("Reading") }
        guard let readingSubjectId = readingSubject?.id else { return }

        // Elementary Reading
        let phonicsLesson = Lesson(
            title: "Letter Sounds and Phonics",
            description: "Learning letter sounds and simple words",
            subjectId: readingSubjectId,
            gradeLevel: .kindergarten,
            duration: 30
        )
        lessons.append(phonicsLesson)

        let comprehensionLesson = Lesson(
            title: "Reading Comprehension Strategies",
            description: "Understanding what we read",
            subjectId: readingSubjectId,
            gradeLevel: .grade3,
            duration: 45
        )
        lessons.append(comprehensionLesson)

        // Middle School Reading
        let literatureLesson = Lesson(
            title: "Analyzing Character Development",
            description: "Understanding how characters change in stories",
            subjectId: readingSubjectId,
            gradeLevel: .grade7,
            duration: 55
        )
        lessons.append(literatureLesson)

        // High School Reading
        let poetryLesson = Lesson(
            title: "Poetry Analysis and Interpretation",
            description: "Understanding poetic devices and meaning",
            subjectId: readingSubjectId,
            gradeLevel: .grade10,
            duration: 90
        )
        lessons.append(poetryLesson)
    }

    private func loadSocialStudiesLessons() {
        let socialStudiesSubject = subjectDataService.allSubjects.first { $0.name.contains("Social Studies") }
        guard let socialStudiesSubjectId = socialStudiesSubject?.id else { return }

        // Elementary Social Studies
        let communityLesson = Lesson(
            title: "Our Community Helpers",
            description: "Learning about people who help in our community",
            subjectId: socialStudiesSubjectId,
            gradeLevel: .grade1,
            duration: 40
        )
        lessons.append(communityLesson)

        let geographyLesson = Lesson(
            title: "Maps and Globes",
            description: "Understanding how to read maps and locate places",
            subjectId: socialStudiesSubjectId,
            gradeLevel: .grade4,
            duration: 50
        )
        lessons.append(geographyLesson)

        // Middle School Social Studies
        let historyLesson = Lesson(
            title: "Ancient Civilizations",
            description: "Exploring early human societies",
            subjectId: socialStudiesSubjectId,
            gradeLevel: .grade6,
            duration: 60
        )
        lessons.append(historyLesson)

        // High School Social Studies
        let governmentLesson = Lesson(
            title: "The Constitution and Bill of Rights",
            description: "Understanding the foundation of American government",
            subjectId: socialStudiesSubjectId,
            gradeLevel: .grade11,
            duration: 90
        )
        lessons.append(governmentLesson)
    }

    private func loadBooks() {
        // Kindergarten Books
        books.append(Book(
            title: "The Very Hungry Caterpillar",
            author: "Eric Carle",
            gradeLevel: .kindergarten,
            genre: .pictureBook,
            pageCount: 26,
            isRecommended: true
        ))

        books.append(Book(
            title: "Brown Bear, Brown Bear, What Do You See?",
            author: "Bill Martin Jr.",
            gradeLevel: .kindergarten,
            genre: .pictureBook,
            pageCount: 32
        ))

        // 1st Grade Books
        books.append(Book(
            title: "Green Eggs and Ham",
            author: "Dr. Seuss",
            gradeLevel: .grade1,
            genre: .fiction,
            pageCount: 62,
            isRecommended: true
        ))

        books.append(Book(
            title: "Frog and Toad Are Friends",
            author: "Arnold Lobel",
            gradeLevel: .grade1,
            genre: .fiction,
            pageCount: 64
        ))

        // 3rd Grade Books
        books.append(Book(
            title: "Charlotte's Web",
            author: "E.B. White",
            gradeLevel: .grade3,
            genre: .fiction,
            pageCount: 184,
            isRecommended: true
        ))

        books.append(Book(
            title: "Magic Tree House: Dinosaurs Before Dark",
            author: "Mary Pope Osborne",
            gradeLevel: .grade3,
            genre: .adventure,
            pageCount: 68
        ))

        // 5th Grade Books
        books.append(Book(
            title: "Bridge to Terabithia",
            author: "Katherine Paterson",
            gradeLevel: .grade5,
            genre: .fiction,
            pageCount: 163,
            isRecommended: true
        ))

        books.append(Book(
            title: "Hatchet",
            author: "Gary Paulsen",
            gradeLevel: .grade5,
            genre: .adventure,
            pageCount: 195
        ))

        // Middle School Books
        books.append(Book(
            title: "The Giver",
            author: "Lois Lowry",
            gradeLevel: .grade7,
            genre: .scienceFiction,
            pageCount: 225,
            isRecommended: true
        ))

        books.append(Book(
            title: "Holes",
            author: "Louis Sachar",
            gradeLevel: .grade7,
            genre: .adventure,
            pageCount: 233
        ))

        // High School Books
        books.append(Book(
            title: "To Kill a Mockingbird",
            author: "Harper Lee",
            gradeLevel: .grade10,
            genre: .fiction,
            pageCount: 376,
            isRecommended: true
        ))

        books.append(Book(
            title: "The Great Gatsby",
            author: "F. Scott Fitzgerald",
            gradeLevel: .grade11,
            genre: .fiction,
            pageCount: 180
        ))
    }

    private func loadExperiments() {
        // Elementary Experiments
        experiments.append(ScienceExperiment(
            title: "Growing Bean Plants",
            description: "Observe how plants grow from seeds",
            objective: "Understand plant life cycles and growth requirements",
            gradeLevel: .grade2,
            domain: .biology
        ))

        experiments.append(ScienceExperiment(
            title: "Volcano Eruption",
            description: "Create a safe volcano eruption using baking soda and vinegar",
            objective: "Learn about chemical reactions and volcanic activity",
            gradeLevel: .grade4,
            domain: .chemistry
        ))

        experiments.append(ScienceExperiment(
            title: "Simple Circuits",
            description: "Build basic electrical circuits with batteries and bulbs",
            objective: "Understand how electricity flows in circuits",
            gradeLevel: .grade5,
            domain: .physics
        ))

        // Middle School Experiments
        experiments.append(ScienceExperiment(
            title: "DNA Extraction from Strawberries",
            description: "Extract DNA from strawberries using household materials",
            objective: "Learn about DNA structure and extraction methods",
            gradeLevel: .grade7,
            domain: .biology
        ))

        experiments.append(ScienceExperiment(
            title: "Acid-Base Indicators",
            description: "Test various substances to determine if they are acids or bases",
            objective: "Understand pH and chemical properties of substances",
            gradeLevel: .grade8,
            domain: .chemistry
        ))

        // High School Experiments
        experiments.append(ScienceExperiment(
            title: "Pendulum Motion Analysis",
            description: "Study the factors affecting pendulum motion",
            objective: "Understand periodic motion and gravitational effects",
            gradeLevel: .grade9,
            domain: .physics
        ))

        experiments.append(ScienceExperiment(
            title: "Enzyme Activity Investigation",
            description: "Study how temperature affects enzyme activity",
            objective: "Understand enzyme function and environmental factors",
            gradeLevel: .grade11,
            domain: .biology
        ))
    }

    private func loadArtProjects() {
        // Elementary Art Projects
        artProjects.append(ArtProject(
            title: "Finger Painting Fun",
            description: "Create colorful artwork using finger paints",
            artForm: .painting,
            gradeLevel: .kindergarten
        ))

        artProjects.append(ArtProject(
            title: "Paper Plate Animals",
            description: "Make animal faces using paper plates and craft materials",
            artForm: .mixedMedia,
            gradeLevel: .grade1
        ))

        artProjects.append(ArtProject(
            title: "Self-Portrait Drawing",
            description: "Draw a detailed self-portrait using pencils",
            artForm: .drawing,
            gradeLevel: .grade3
        ))

        artProjects.append(ArtProject(
            title: "Clay Pottery Bowls",
            description: "Create functional bowls using clay and pottery techniques",
            artForm: .ceramics,
            gradeLevel: .grade5
        ))

        // Middle School Art Projects
        artProjects.append(ArtProject(
            title: "Perspective Drawing",
            description: "Learn one-point and two-point perspective drawing",
            artForm: .drawing,
            gradeLevel: .grade7
        ))

        artProjects.append(ArtProject(
            title: "Digital Photo Manipulation",
            description: "Edit and enhance digital photographs",
            artForm: .digitalArt,
            gradeLevel: .grade8
        ))

        // High School Art Projects
        artProjects.append(ArtProject(
            title: "Oil Painting Landscape",
            description: "Create a realistic landscape using oil paints",
            artForm: .painting,
            gradeLevel: .grade10
        ))

        artProjects.append(ArtProject(
            title: "3D Sculpture Design",
            description: "Design and create a three-dimensional sculpture",
            artForm: .sculpture,
            gradeLevel: .grade11
        ))

        artProjects.append(ArtProject(
            title: "Stop-Motion Animation",
            description: "Create a short stop-motion animated film",
            artForm: .animation,
            gradeLevel: .grade12
        ))
    }
}
