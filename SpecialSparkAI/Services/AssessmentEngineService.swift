//
//  AssessmentEngineService.swift
//  SpecialSparkAI
//
//  Phase 4: Assessment Engine Enhancement
//

import Foundation
import SwiftUI

@MainActor
class AssessmentEngineService: ObservableObject {
    static let shared = AssessmentEngineService()

    @Published var currentAssessment: Assessment?
    @Published var currentSession: AssessmentSession?
    @Published var isAssessmentActive = false
    @Published var assessmentResults: [AssessmentSessionResult] = []
    @Published var availableAssessments: [Assessment] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let geminiService = GeminiService.shared
    private let adaptiveLearningService = AdaptiveLearningService.shared
    private let supabaseService = SupabaseService.shared

    private init() {
        loadAvailableAssessments()
    }

    // MARK: - Assessment Management

    func createAssessment(
        title: String,
        subject: String,
        gradeLevel: GradeLevel,
        type: AssessmentType,
        difficulty: DifficultyLevel,
        questionCount: Int = 10
    ) async -> Assessment? {
        isLoading = true
        defer { isLoading = false }

        // Generate questions using AI
        let questions = await generateAssessmentQuestions(
            subject: subject,
            gradeLevel: gradeLevel,
            difficulty: difficulty,
            count: questionCount,
            type: type
        )

        let assessment = Assessment(
            id: UUID(),
            title: title,
            description: "AI-generated \(type.rawValue) assessment for \(subject)",
            type: type,
            subject: subject,
            gradeLevel: gradeLevel.displayName,
            difficulty: difficulty,
            estimatedDuration: questionCount * 2, // 2 minutes per question
            questions: questions,
            rubrics: createDefaultRubrics(for: type),
            adaptiveSettings: createAdaptiveSettings(for: difficulty),
            accommodations: createDefaultAccommodations(),
            scoringCriteria: createScoringCriteria(questionCount: questionCount),
            isActive: true,
            createdAt: Date().formatted(),
            updatedAt: Date().formatted()
        )

        availableAssessments.append(assessment)
        return assessment
    }

    func startAssessment(_ assessment: Assessment, for student: Student) async -> AssessmentSession? {
        isLoading = true
        defer { isLoading = false }

        let session = AssessmentSession(
            id: UUID(),
            assessmentId: assessment.id,
            studentId: student.id,
            startTime: Date().formatted(),
            endTime: nil,
            status: .inProgress,
            responses: [],
            score: nil,
            adaptiveData: AdaptiveSessionData(
                abilityEstimate: nil,
                standardError: nil,
                questionsAdministered: 0,
                adaptationHistory: [],
                terminationReason: nil
            ),
            accommodationsUsed: [],
            technicalIssues: [],
            proctorNotes: []
        )

        currentAssessment = assessment
        currentSession = session
        isAssessmentActive = true

        return session
    }

    func submitResponse(
        questionId: UUID,
        response: String,
        responseTime: TimeInterval
    ) async {
        guard let session = currentSession,
              let assessment = currentAssessment else { return }

        // Find the question
        guard let question = assessment.questions.first(where: { $0.id == questionId }) else { return }

        // Evaluate response
        let isCorrect = evaluateResponse(response, for: question)
        let score = calculateQuestionScore(question: question, isCorrect: isCorrect, responseTime: responseTime)

        // Create response record
        let assessmentResponse = AssessmentResponse(
            id: UUID(),
            questionId: questionId,
            response: response,
            isCorrect: isCorrect,
            pointsEarned: Int(score),
            timeSpent: Int(responseTime),
            hintsUsed: [],
            attempts: 1,
            confidence: 0.5,
            responseMetadata: ResponseMetadata(
                keystrokeData: nil,
                mouseMovements: nil,
                eyeTracking: nil,
                audioLevel: nil,
                screenRecording: nil
            )
        )

        // Update session with new response
        let updatedSession = AssessmentSession(
            id: session.id,
            assessmentId: session.assessmentId,
            studentId: session.studentId,
            startTime: session.startTime,
            endTime: session.endTime,
            status: session.status,
            responses: session.responses + [assessmentResponse],
            score: session.score,
            adaptiveData: session.adaptiveData,
            accommodationsUsed: session.accommodationsUsed,
            technicalIssues: session.technicalIssues,
            proctorNotes: session.proctorNotes
        )
        currentSession = updatedSession

        // Adaptive adjustment if needed
        if assessment.adaptiveSettings.isAdaptive {
            await performAdaptiveAdjustment(session: session, lastResponse: assessmentResponse)
        }
    }

    func completeAssessment() async -> AssessmentSessionResult? {
        guard let session = currentSession,
              let assessment = currentAssessment else { return nil }

        // Calculate final score
        let finalScore = calculateFinalScore(session: session, assessment: assessment)

        // Create completed session
        let completedSession = AssessmentSession(
            id: session.id,
            assessmentId: session.assessmentId,
            studentId: session.studentId,
            startTime: session.startTime,
            endTime: Date().formatted(),
            status: .completed,
            responses: session.responses,
            score: finalScore,
            adaptiveData: session.adaptiveData,
            accommodationsUsed: session.accommodationsUsed,
            technicalIssues: session.technicalIssues,
            proctorNotes: session.proctorNotes
        )

        // Create assessment result
        let result = AssessmentSessionResult(
            id: UUID(),
            sessionId: completedSession.id,
            assessmentId: assessment.id,
            studentId: completedSession.studentId,
            score: finalScore,
            completedAt: Date().formatted(),
            timeSpent: calculateTimeSpent(session: completedSession),
            analytics: generateAnalytics(session: completedSession, assessment: assessment)
        )

        assessmentResults.append(result)

        // Reset current state
        currentAssessment = nil
        currentSession = nil
        isAssessmentActive = false

        // Update adaptive learning system
        await updateAdaptiveLearning(result: result)

        return result
    }

    // MARK: - AI-Powered Question Generation

    private func generateAssessmentQuestions(
        subject: String,
        gradeLevel: GradeLevel,
        difficulty: DifficultyLevel,
        count: Int,
        type: AssessmentType
    ) async -> [AssessmentQuestion] {
        let prompt = buildQuestionGenerationPrompt(
            subject: subject,
            gradeLevel: gradeLevel,
            difficulty: difficulty,
            count: count,
            type: type
        )

        let response = await geminiService.generateResponse(
            prompt: prompt,
            context: "Assessment question generation"
        )

        return parseGeneratedQuestions(response, difficulty: difficulty)
    }

    private func buildQuestionGenerationPrompt(
        subject: String,
        gradeLevel: GradeLevel,
        difficulty: DifficultyLevel,
        count: Int,
        type: AssessmentType
    ) -> String {
        return """
        Generate \(count) \(type.rawValue) assessment questions for \(subject) at \(gradeLevel.displayName) level with \(difficulty.rawValue) difficulty.

        Requirements:
        - Questions should be age-appropriate and engaging
        - Include multiple choice, true/false, and short answer formats
        - Provide clear, unambiguous correct answers
        - Include learning objectives for each question
        - Consider special needs accommodations
        - Ensure questions test different cognitive levels

        Format each question as:
        Question: [question text]
        Type: [multiple_choice/true_false/short_answer]
        Options: [if applicable]
        Correct Answer: [answer]
        Learning Objective: [objective]
        Bloom's Level: [level]
        Points: [1-5]

        Subject: \(subject)
        Grade Level: \(gradeLevel.displayName)
        Difficulty: \(difficulty.rawValue)
        Assessment Type: \(type.rawValue)
        """
    }

    private func parseGeneratedQuestions(_ response: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        // Parse AI response and create AssessmentQuestion objects
        // This is a simplified implementation - in production, you'd use more sophisticated parsing

        var questions: [AssessmentQuestion] = []
        let questionBlocks = response.components(separatedBy: "Question:")

        for (_, block) in questionBlocks.enumerated() {
            if block.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty { continue }

            let question = AssessmentQuestion(
                id: UUID(),
                questionText: extractQuestionText(from: block),
                questionType: extractQuestionType(from: block),
                options: extractOptions(from: block),
                correctAnswers: extractCorrectAnswers(from: block),
                points: extractPoints(from: block),
                difficulty: mapToQuestionDifficulty(difficulty),
                bloomsLevel: extractBloomsLevel(from: block),
                learningObjective: extractLearningObjective(from: block),
                hints: generateHints(for: block),
                multimedia: [],
                adaptiveRules: [],
                timeLimit: nil,
                allowPartialCredit: false
            )

            questions.append(question)
        }

        return questions
    }

    // MARK: - Helper Methods

    private func loadAvailableAssessments() {
        // Load sample assessments
        availableAssessments = createSampleAssessments()
    }

    private func createSampleAssessments() -> [Assessment] {
        return [
            createSampleMathAssessment(),
            createSampleReadingAssessment(),
            createSampleScienceAssessment()
        ]
    }

    private func createSampleMathAssessment() -> Assessment {
        return Assessment(
            id: UUID(),
            title: "Basic Addition and Subtraction",
            description: "Assessment covering basic arithmetic operations",
            type: .formative,
            subject: "Mathematics",
            gradeLevel: "Grade 2",
            difficulty: .beginner,
            estimatedDuration: 15,
            questions: createSampleMathQuestions(),
            rubrics: createDefaultRubrics(for: .formative),
            adaptiveSettings: createAdaptiveSettings(for: .beginner),
            accommodations: createDefaultAccommodations(),
            scoringCriteria: createScoringCriteria(questionCount: 5),
            isActive: true,
            createdAt: Date().formatted(),
            updatedAt: Date().formatted()
        )
    }

    private func createSampleMathQuestions() -> [AssessmentQuestion] {
        return [
            AssessmentQuestion(
                id: UUID(),
                questionText: "What is 5 + 3?",
                questionType: .multipleChoice,
                options: [
                    QuestionOption(id: UUID(), text: "6", isCorrect: false, feedback: "Try again", multimedia: nil),
                    QuestionOption(id: UUID(), text: "7", isCorrect: false, feedback: "Close, but not quite", multimedia: nil),
                    QuestionOption(id: UUID(), text: "8", isCorrect: true, feedback: "Correct! Great job!", multimedia: nil),
                    QuestionOption(id: UUID(), text: "9", isCorrect: false, feedback: "Too high", multimedia: nil)
                ],
                correctAnswers: ["8"],
                points: 1,
                difficulty: .easy,
                bloomsLevel: .remember,
                learningObjective: "Perform basic addition",
                hints: [Hint(id: UUID(), text: "Count on your fingers if needed", level: .gentle, pointDeduction: 0, multimedia: nil)],
                multimedia: [],
                adaptiveRules: [],
                timeLimit: 60,
                allowPartialCredit: false
            )
        ]
    }

    // MARK: - Helper Methods Implementation

    private func extractQuestionText(from block: String) -> String {
        // Extract question text from AI response block
        let lines = block.components(separatedBy: .newlines)
        return lines.first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? "Sample Question"
    }

    private func extractQuestionType(from block: String) -> QuestionType {
        if block.contains("multiple_choice") { return .multipleChoice }
        if block.contains("true_false") { return .trueFalse }
        return .shortAnswer
    }

    private func extractOptions(from block: String) -> [QuestionOption]? {
        // Parse options from AI response
        return nil // Simplified for now
    }

    private func extractCorrectAnswers(from block: String) -> [String] {
        // Extract correct answers from AI response
        return ["Sample Answer"]
    }

    private func extractPoints(from block: String) -> Int {
        // Extract point value from AI response
        return 1
    }

    private func mapToQuestionDifficulty(_ difficulty: DifficultyLevel) -> QuestionDifficulty {
        switch difficulty {
        case .beginner: return .easy
        case .intermediate: return .medium
        case .advanced: return .hard
        case .expert: return .veryHard
        }
    }

    private func extractBloomsLevel(from block: String) -> BloomsLevel {
        return .remember // Default
    }

    private func extractLearningObjective(from block: String) -> String {
        return "Sample learning objective"
    }

    private func generateHints(for block: String) -> [Hint] {
        return [Hint(id: UUID(), text: "Think step by step", level: .gentle, pointDeduction: 0, multimedia: nil)]
    }

    private func evaluateResponse(_ response: String, for question: AssessmentQuestion) -> Bool {
        return question.correctAnswers.contains(response.trimmingCharacters(in: .whitespacesAndNewlines))
    }

    private func calculateQuestionScore(question: AssessmentQuestion, isCorrect: Bool, responseTime: TimeInterval) -> Double {
        if !isCorrect { return 0.0 }

        // Base score
        var score = Double(question.points)

        // Time bonus (if answered quickly)
        if let timeLimit = question.timeLimit, responseTime < Double(timeLimit) * 0.5 {
            score *= 1.1 // 10% bonus for quick correct answers
        }

        return score
    }

    private func performAdaptiveAdjustment(session: AssessmentSession, lastResponse: AssessmentResponse) async {
        // Implement adaptive logic based on performance
        let recentAccuracy = calculateRecentAccuracy(session: session)

        if recentAccuracy < 0.5 {
            // Decrease difficulty or provide hints
            await adjustDifficultyDown(session: session)
        } else if recentAccuracy > 0.8 {
            // Increase difficulty
            await adjustDifficultyUp(session: session)
        }
    }

    private func calculateRecentAccuracy(session: AssessmentSession) -> Double {
        let recentResponses = Array(session.responses.suffix(3))
        guard !recentResponses.isEmpty else { return 0.5 }

        let correctCount = recentResponses.filter { $0.isCorrect ?? false }.count
        return Double(correctCount) / Double(recentResponses.count)
    }

    private func adjustDifficultyDown(session: AssessmentSession) async {
        // Implementation for decreasing difficulty
    }

    private func adjustDifficultyUp(session: AssessmentSession) async {
        // Implementation for increasing difficulty
    }

    private func calculateFinalScore(session: AssessmentSession, assessment: Assessment) -> AssessmentScore {
        let earnedPoints = session.responses.reduce(0) { $0 + $1.pointsEarned }
        let maxPoints = assessment.questions.reduce(0) { $0 + $1.points }
        let percentage = maxPoints > 0 ? (Double(earnedPoints) / Double(maxPoints)) * 100 : 0

        return AssessmentScore(
            totalPoints: maxPoints,
            earnedPoints: earnedPoints,
            percentage: percentage,
            grade: calculateLetterGrade(percentage: percentage),
            proficiencyLevel: calculateProficiencyLevel(percentage: percentage),
            subscores: [],
            standardError: nil,
            confidenceInterval: nil
        )
    }

    private func calculateLetterGrade(percentage: Double) -> String {
        switch percentage {
        case 90...100: return "A"
        case 80..<90: return "B"
        case 70..<80: return "C"
        case 60..<70: return "D"
        default: return "F"
        }
    }

    private func calculateProficiencyLevel(percentage: Double) -> String {
        switch percentage {
        case 90...100: return "Advanced"
        case 80..<90: return "Proficient"
        case 70..<80: return "Developing"
        case 60..<70: return "Beginning"
        default: return "Below Basic"
        }
    }

    private func calculateAverageResponseTime(session: AssessmentSession) -> Double {
        guard !session.responses.isEmpty else { return 0.0 }
        let totalTime = session.responses.reduce(0) { $0 + $1.timeSpent }
        return Double(totalTime) / Double(session.responses.count)
    }

    private func calculateTimeSpent(session: AssessmentSession) -> TimeInterval {
        guard let startTime = ISO8601DateFormatter().date(from: session.startTime),
              let endTime = session.endTime.flatMap({ ISO8601DateFormatter().date(from: $0) }) else {
            return 0
        }
        return endTime.timeIntervalSince(startTime)
    }

    private func generateAnalytics(session: AssessmentSession, assessment: Assessment) -> AssessmentAnalytics {
        return AssessmentAnalytics(
            assessmentId: assessment.id,
            totalSessions: 1,
            completionRate: 1.0,
            averageScore: session.score?.percentage ?? 0.0,
            averageDuration: Int(calculateTimeSpent(session: session)),
            difficultyAnalysis: DifficultyAnalysis(
                averageDifficulty: 0.5,
                difficultyRange: 0.0...1.0,
                optimalDifficulty: 0.6,
                recommendations: ["Continue with current difficulty level"]
            ),
            itemAnalysis: [],
            performanceDistribution: PerformanceDistribution(
                mean: session.score?.percentage ?? 0.0,
                median: session.score?.percentage ?? 0.0,
                mode: session.score?.percentage ?? 0.0,
                standardDeviation: 0.0,
                skewness: 0.0,
                kurtosis: 0.0,
                percentiles: []
            ),
            reliabilityMetrics: ReliabilityMetrics(
                cronbachAlpha: 0.8,
                splitHalf: 0.85,
                testRetest: 0.9,
                standardError: 0.1
            ),
            validityIndicators: ValidityIndicators(
                contentValidity: 0.9,
                constructValidity: 0.85,
                criterionValidity: 0.8,
                faceValidity: 0.9
            )
        )
    }

    private func updateAdaptiveLearning(result: AssessmentSessionResult) async {
        // Update the adaptive learning system with assessment results
        await adaptiveLearningService.updateLearningProgress(
            studentId: result.studentId,
            subjectId: UUID(), // Would be derived from assessment
            skillId: UUID(), // Would be derived from assessment
            assessmentResult: AssessmentRecord(
                id: UUID(),
                studentId: result.studentId,
                sessionId: result.sessionId,
                questionId: UUID(),
                questionText: "Assessment completed",
                studentAnswer: "Completed",
                correctAnswer: "Completed",
                isCorrect: result.score.percentage >= 70.0,
                responseTime: result.timeSpent,
                hintsUsed: 0,
                difficultyLevel: "medium",
                skillTested: "Overall assessment",
                adaptiveAdjustment: nil,
                createdAt: result.completedAt
            )
        )
    }

    private func createSampleReadingAssessment() -> Assessment {
        return Assessment(
            id: UUID(),
            title: "Reading Comprehension",
            description: "Assessment covering reading skills and comprehension",
            type: .summative,
            subject: "Reading",
            gradeLevel: "Grade 3",
            difficulty: .intermediate,
            estimatedDuration: 20,
            questions: [],
            rubrics: createDefaultRubrics(for: .summative),
            adaptiveSettings: createAdaptiveSettings(for: .intermediate),
            accommodations: createDefaultAccommodations(),
            scoringCriteria: createScoringCriteria(questionCount: 8),
            isActive: true,
            createdAt: Date().formatted(),
            updatedAt: Date().formatted()
        )
    }

    private func createSampleScienceAssessment() -> Assessment {
        return Assessment(
            id: UUID(),
            title: "Basic Science Concepts",
            description: "Assessment covering fundamental science concepts",
            type: .diagnostic,
            subject: "Science",
            gradeLevel: "Grade 4",
            difficulty: .intermediate,
            estimatedDuration: 25,
            questions: [],
            rubrics: createDefaultRubrics(for: .diagnostic),
            adaptiveSettings: createAdaptiveSettings(for: .intermediate),
            accommodations: createDefaultAccommodations(),
            scoringCriteria: createScoringCriteria(questionCount: 10),
            isActive: true,
            createdAt: Date().formatted(),
            updatedAt: Date().formatted()
        )
    }

    private func createDefaultRubrics(for type: AssessmentType) -> [AssessmentRubric] {
        return [
            AssessmentRubric(
                id: UUID(),
                name: "Standard Rubric",
                description: "Standard scoring rubric for \(type.rawValue) assessments",
                criteria: [
                    RubricCriterion(
                        id: UUID(),
                        name: "Accuracy",
                        description: "Correctness of answers",
                        weight: 0.7,
                        performanceLevels: [
                            PerformanceLevel(id: UUID(), level: 4, name: "Excellent", description: "90-100% correct", points: 4, exemplars: []),
                            PerformanceLevel(id: UUID(), level: 3, name: "Good", description: "80-89% correct", points: 3, exemplars: []),
                            PerformanceLevel(id: UUID(), level: 2, name: "Fair", description: "70-79% correct", points: 2, exemplars: []),
                            PerformanceLevel(id: UUID(), level: 1, name: "Needs Improvement", description: "Below 70% correct", points: 1, exemplars: [])
                        ]
                    )
                ],
                scoringScale: .fourPoint,
                isHolistic: false
            )
        ]
    }

    private func createAdaptiveSettings(for difficulty: DifficultyLevel) -> AdaptiveAssessmentSettings {
        return AdaptiveAssessmentSettings(
            isAdaptive: true,
            adaptationAlgorithm: .irt,
            minimumQuestions: 5,
            maximumQuestions: 20,
            terminationCriteria: [
                TerminationCriterion(
                    id: UUID(),
                    type: .standardError,
                    threshold: 0.3,
                    priority: 1
                )
            ],
            difficultyAdjustment: DifficultyAdjustment(
                initialDifficulty: 0.5,
                adjustmentRate: 0.2,
                minimumDifficulty: 0.1,
                maximumDifficulty: 0.9,
                smoothingFactor: 0.3
            ),
            contentBalancing: ContentBalancing(
                enforceBalance: true,
                contentAreas: [],
                balancingStrategy: .weighted
            )
        )
    }

    private func createDefaultAccommodations() -> [AssessmentAccommodation] {
        return [
            AssessmentAccommodation(
                id: UUID(),
                type: .timing,
                description: "Additional time for students who need it",
                implementation: AccommodationImplementation(
                    settings: ["timeMultiplier": "1.5"],
                    instructions: "Provide 50% additional time",
                    verification: "Monitor time usage"
                ),
                isRequired: false,
                studentIds: []
            ),
            AssessmentAccommodation(
                id: UUID(),
                type: .presentation,
                description: "Text-to-speech for questions",
                implementation: AccommodationImplementation(
                    settings: ["voice": "natural", "speed": "normal"],
                    instructions: "Enable text-to-speech",
                    verification: "Confirm audio is working"
                ),
                isRequired: false,
                studentIds: []
            )
        ]
    }

    private func createScoringCriteria(questionCount: Int) -> ScoringCriteria {
        return ScoringCriteria(
            totalPoints: questionCount,
            passingScore: 0.7,
            gradingScale: GradingScale(
                type: .percentage,
                ranges: [
                    GradeRange(id: UUID(), grade: "A", minimumScore: 90, maximumScore: 100, description: "Excellent"),
                    GradeRange(id: UUID(), grade: "B", minimumScore: 80, maximumScore: 89, description: "Good"),
                    GradeRange(id: UUID(), grade: "C", minimumScore: 70, maximumScore: 79, description: "Satisfactory"),
                    GradeRange(id: UUID(), grade: "D", minimumScore: 60, maximumScore: 69, description: "Needs Improvement"),
                    GradeRange(id: UUID(), grade: "F", minimumScore: 0, maximumScore: 59, description: "Failing")
                ]
            ),
            weightedScoring: false,
            partialCreditRules: [],
            bonusPoints: []
        )
    }
}

// MARK: - Supporting Structures

struct AssessmentSessionResult: Identifiable, Codable {
    let id: UUID
    let sessionId: UUID
    let assessmentId: UUID
    let studentId: UUID
    let score: AssessmentScore
    let completedAt: String
    let timeSpent: TimeInterval
    let analytics: AssessmentAnalytics
}
