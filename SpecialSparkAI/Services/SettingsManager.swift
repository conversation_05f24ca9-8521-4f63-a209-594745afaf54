//
//  SettingsManager.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Settings Enums
enum ContrastLevel: String, CaseIterable {
    case standard = "standard"
    case high = "high"
    case maximum = "maximum"
}

// Voice enums are defined in AITeacherModels.swift

enum TouchSensitivity: String, CaseIterable {
    case light = "light"
    case normal = "normal"
    case firm = "firm"
}

enum VisualComplexity: String, CaseIterable {
    case minimal = "minimal"
    case simple = "simple"
    case standard = "standard"
    case rich = "rich"
}

// MARK: - Settings Manager
class SettingsManager: ObservableObject {
    static let shared = SettingsManager()

    private init() {
        loadSettings()
    }

    // MARK: - Profile Settings
    @Published var currentGrade: String = "Grade 3"
    @Published var gradeChangeNotification = false

    // MARK: - Accessibility Settings
    @Published var highContrastMode: Bool = false
    @Published var reduceMotion: Bool = false
    @Published var largeText: Bool = false
    @Published var contrastLevel: ContrastLevel = .standard

    // MARK: - Visual Settings
    @Published var reduceRedGreen: Bool = false
    @Published var reduceBlueYellow: Bool = false
    @Published var grayscaleMode: Bool = false
    @Published var textSizeMultiplier: Double = 1.0
    @Published var reduceBrightColors: Bool = false
    @Published var disableFlashing: Bool = false
    @Published var preferDarkMode: Bool = false

    // MARK: - Audio Settings
    @Published var uiSoundVolume: Double = 0.7
    @Published var voiceVolume: Double = 0.8
    @Published var backgroundMusicVolume: Double = 0.3
    @Published var muteAllSounds: Bool = false
    @Published var reduceSuddenSounds: Bool = false
    @Published var enableAudioDescriptions: Bool = false
    @Published var voiceSpeed: VoiceSpeed = .moderate
    @Published var voiceType: VoiceType = .gentle

    // MARK: - Motion Settings
    @Published var disableAutoPlay: Bool = false
    @Published var slowerTransitions: Bool = false
    @Published var animationSpeed: Double = 1.0

    // MARK: - Touch Settings
    @Published var touchSensitivity: TouchSensitivity = .normal
    @Published var hapticFeedback: Bool = true
    @Published var buttonHoldDelay: Bool = false
    @Published var preventAccidentalTaps: Bool = false
    @Published var responseTime: Double = 1.0

    // MARK: - Focus Settings
    @Published var minimizeDistractions: Bool = false
    @Published var focusMode: Bool = false
    @Published var simplifiedInterface: Bool = false
    @Published var visualComplexity: VisualComplexity = .standard
    @Published var breakReminders: Bool = true
    @Published var showProgressIndicators: Bool = true

    // MARK: - Learning Settings
    @Published var adaptiveLearning: Bool = true

    // MARK: - Notification Settings
    @Published var pushNotifications: Bool = true
    @Published var achievementNotifications: Bool = true
    @Published var reminderNotifications: Bool = true

    // MARK: - UserDefaults Keys
    private enum Keys {
        static let currentGrade = "currentGrade"
        static let highContrastMode = "highContrastMode"
        static let reduceMotion = "reduceMotion"
        static let largeText = "largeText"
        static let contrastLevel = "contrastLevel"
        static let reduceRedGreen = "reduceRedGreen"
        static let reduceBlueYellow = "reduceBlueYellow"
        static let grayscaleMode = "grayscaleMode"
        static let textSizeMultiplier = "textSizeMultiplier"
        static let reduceBrightColors = "reduceBrightColors"
        static let disableFlashing = "disableFlashing"
        static let preferDarkMode = "preferDarkMode"
        static let uiSoundVolume = "uiSoundVolume"
        static let voiceVolume = "voiceVolume"
        static let backgroundMusicVolume = "backgroundMusicVolume"
        static let muteAllSounds = "muteAllSounds"
        static let reduceSuddenSounds = "reduceSuddenSounds"
        static let enableAudioDescriptions = "enableAudioDescriptions"
        static let voiceSpeed = "voiceSpeed"
        static let voiceType = "voiceType"
        static let disableAutoPlay = "disableAutoPlay"
        static let slowerTransitions = "slowerTransitions"
        static let animationSpeed = "animationSpeed"
        static let touchSensitivity = "touchSensitivity"
        static let hapticFeedback = "hapticFeedback"
        static let buttonHoldDelay = "buttonHoldDelay"
        static let preventAccidentalTaps = "preventAccidentalTaps"
        static let responseTime = "responseTime"
        static let minimizeDistractions = "minimizeDistractions"
        static let focusMode = "focusMode"
        static let simplifiedInterface = "simplifiedInterface"
        static let visualComplexity = "visualComplexity"
        static let breakReminders = "breakReminders"
        static let showProgressIndicators = "showProgressIndicators"
        static let adaptiveLearning = "adaptiveLearning"
        static let pushNotifications = "pushNotifications"
        static let achievementNotifications = "achievementNotifications"
        static let reminderNotifications = "reminderNotifications"
    }

    // MARK: - Load Settings
    private func loadSettings() {
        let defaults = UserDefaults.standard

        currentGrade = defaults.string(forKey: Keys.currentGrade) ?? "Grade 3"
        highContrastMode = defaults.bool(forKey: Keys.highContrastMode)
        reduceMotion = defaults.bool(forKey: Keys.reduceMotion)
        largeText = defaults.bool(forKey: Keys.largeText)

        if let contrastString = defaults.string(forKey: Keys.contrastLevel),
           let contrast = ContrastLevel(rawValue: contrastString) {
            contrastLevel = contrast
        }

        reduceRedGreen = defaults.bool(forKey: Keys.reduceRedGreen)
        reduceBlueYellow = defaults.bool(forKey: Keys.reduceBlueYellow)
        grayscaleMode = defaults.bool(forKey: Keys.grayscaleMode)
        textSizeMultiplier = defaults.double(forKey: Keys.textSizeMultiplier) == 0 ? 1.0 : defaults.double(forKey: Keys.textSizeMultiplier)
        reduceBrightColors = defaults.bool(forKey: Keys.reduceBrightColors)
        disableFlashing = defaults.bool(forKey: Keys.disableFlashing)
        preferDarkMode = defaults.bool(forKey: Keys.preferDarkMode)

        uiSoundVolume = defaults.double(forKey: Keys.uiSoundVolume) == 0 ? 0.7 : defaults.double(forKey: Keys.uiSoundVolume)
        voiceVolume = defaults.double(forKey: Keys.voiceVolume) == 0 ? 0.8 : defaults.double(forKey: Keys.voiceVolume)
        backgroundMusicVolume = defaults.double(forKey: Keys.backgroundMusicVolume) == 0 ? 0.3 : defaults.double(forKey: Keys.backgroundMusicVolume)
        muteAllSounds = defaults.bool(forKey: Keys.muteAllSounds)
        reduceSuddenSounds = defaults.bool(forKey: Keys.reduceSuddenSounds)
        enableAudioDescriptions = defaults.bool(forKey: Keys.enableAudioDescriptions)

        if let voiceSpeedString = defaults.string(forKey: Keys.voiceSpeed),
           let speed = VoiceSpeed(rawValue: voiceSpeedString) {
            voiceSpeed = speed
        }

        if let voiceTypeString = defaults.string(forKey: Keys.voiceType),
           let type = VoiceType(rawValue: voiceTypeString) {
            voiceType = type
        }

        disableAutoPlay = defaults.bool(forKey: Keys.disableAutoPlay)
        slowerTransitions = defaults.bool(forKey: Keys.slowerTransitions)
        animationSpeed = defaults.double(forKey: Keys.animationSpeed) == 0 ? 1.0 : defaults.double(forKey: Keys.animationSpeed)

        if let touchString = defaults.string(forKey: Keys.touchSensitivity),
           let touch = TouchSensitivity(rawValue: touchString) {
            touchSensitivity = touch
        }

        hapticFeedback = defaults.object(forKey: Keys.hapticFeedback) == nil ? true : defaults.bool(forKey: Keys.hapticFeedback)
        buttonHoldDelay = defaults.bool(forKey: Keys.buttonHoldDelay)
        preventAccidentalTaps = defaults.bool(forKey: Keys.preventAccidentalTaps)
        responseTime = defaults.double(forKey: Keys.responseTime) == 0 ? 1.0 : defaults.double(forKey: Keys.responseTime)

        minimizeDistractions = defaults.bool(forKey: Keys.minimizeDistractions)
        focusMode = defaults.bool(forKey: Keys.focusMode)
        simplifiedInterface = defaults.bool(forKey: Keys.simplifiedInterface)

        if let complexityString = defaults.string(forKey: Keys.visualComplexity),
           let complexity = VisualComplexity(rawValue: complexityString) {
            visualComplexity = complexity
        }

        breakReminders = defaults.object(forKey: Keys.breakReminders) == nil ? true : defaults.bool(forKey: Keys.breakReminders)
        showProgressIndicators = defaults.object(forKey: Keys.showProgressIndicators) == nil ? true : defaults.bool(forKey: Keys.showProgressIndicators)
        adaptiveLearning = defaults.object(forKey: Keys.adaptiveLearning) == nil ? true : defaults.bool(forKey: Keys.adaptiveLearning)
        pushNotifications = defaults.object(forKey: Keys.pushNotifications) == nil ? true : defaults.bool(forKey: Keys.pushNotifications)
        achievementNotifications = defaults.object(forKey: Keys.achievementNotifications) == nil ? true : defaults.bool(forKey: Keys.achievementNotifications)
        reminderNotifications = defaults.object(forKey: Keys.reminderNotifications) == nil ? true : defaults.bool(forKey: Keys.reminderNotifications)
    }

    // MARK: - Save Settings
    func saveSettings() {
        let defaults = UserDefaults.standard

        defaults.set(currentGrade, forKey: Keys.currentGrade)
        defaults.set(highContrastMode, forKey: Keys.highContrastMode)
        defaults.set(reduceMotion, forKey: Keys.reduceMotion)
        defaults.set(largeText, forKey: Keys.largeText)
        defaults.set(contrastLevel.rawValue, forKey: Keys.contrastLevel)
        defaults.set(reduceRedGreen, forKey: Keys.reduceRedGreen)
        defaults.set(reduceBlueYellow, forKey: Keys.reduceBlueYellow)
        defaults.set(grayscaleMode, forKey: Keys.grayscaleMode)
        defaults.set(textSizeMultiplier, forKey: Keys.textSizeMultiplier)
        defaults.set(reduceBrightColors, forKey: Keys.reduceBrightColors)
        defaults.set(disableFlashing, forKey: Keys.disableFlashing)
        defaults.set(preferDarkMode, forKey: Keys.preferDarkMode)
        defaults.set(uiSoundVolume, forKey: Keys.uiSoundVolume)
        defaults.set(voiceVolume, forKey: Keys.voiceVolume)
        defaults.set(backgroundMusicVolume, forKey: Keys.backgroundMusicVolume)
        defaults.set(muteAllSounds, forKey: Keys.muteAllSounds)
        defaults.set(reduceSuddenSounds, forKey: Keys.reduceSuddenSounds)
        defaults.set(enableAudioDescriptions, forKey: Keys.enableAudioDescriptions)
        defaults.set(voiceSpeed.rawValue, forKey: Keys.voiceSpeed)
        defaults.set(voiceType.rawValue, forKey: Keys.voiceType)
        defaults.set(disableAutoPlay, forKey: Keys.disableAutoPlay)
        defaults.set(slowerTransitions, forKey: Keys.slowerTransitions)
        defaults.set(animationSpeed, forKey: Keys.animationSpeed)
        defaults.set(touchSensitivity.rawValue, forKey: Keys.touchSensitivity)
        defaults.set(hapticFeedback, forKey: Keys.hapticFeedback)
        defaults.set(buttonHoldDelay, forKey: Keys.buttonHoldDelay)
        defaults.set(preventAccidentalTaps, forKey: Keys.preventAccidentalTaps)
        defaults.set(responseTime, forKey: Keys.responseTime)
        defaults.set(minimizeDistractions, forKey: Keys.minimizeDistractions)
        defaults.set(focusMode, forKey: Keys.focusMode)
        defaults.set(simplifiedInterface, forKey: Keys.simplifiedInterface)
        defaults.set(visualComplexity.rawValue, forKey: Keys.visualComplexity)
        defaults.set(breakReminders, forKey: Keys.breakReminders)
        defaults.set(showProgressIndicators, forKey: Keys.showProgressIndicators)
        defaults.set(adaptiveLearning, forKey: Keys.adaptiveLearning)
        defaults.set(pushNotifications, forKey: Keys.pushNotifications)
        defaults.set(achievementNotifications, forKey: Keys.achievementNotifications)
        defaults.set(reminderNotifications, forKey: Keys.reminderNotifications)
    }

    // MARK: - Reset Functions
    func resetToDefaults() {
        let defaults = UserDefaults.standard

        // Remove all settings
        for key in [Keys.currentGrade, Keys.highContrastMode, Keys.reduceMotion, Keys.largeText, Keys.contrastLevel,
                   Keys.reduceRedGreen, Keys.reduceBlueYellow, Keys.grayscaleMode, Keys.textSizeMultiplier,
                   Keys.reduceBrightColors, Keys.disableFlashing, Keys.preferDarkMode, Keys.uiSoundVolume,
                   Keys.voiceVolume, Keys.backgroundMusicVolume, Keys.muteAllSounds, Keys.reduceSuddenSounds,
                   Keys.enableAudioDescriptions, Keys.voiceSpeed, Keys.voiceType, Keys.disableAutoPlay,
                   Keys.slowerTransitions, Keys.animationSpeed, Keys.touchSensitivity, Keys.hapticFeedback,
                   Keys.buttonHoldDelay, Keys.preventAccidentalTaps, Keys.responseTime, Keys.minimizeDistractions,
                   Keys.focusMode, Keys.simplifiedInterface, Keys.visualComplexity, Keys.breakReminders,
                   Keys.showProgressIndicators, Keys.adaptiveLearning, Keys.pushNotifications,
                   Keys.achievementNotifications, Keys.reminderNotifications] {
            defaults.removeObject(forKey: key)
        }

        // Reload defaults
        loadSettings()
    }

    func resetSensorySettings() {
        // Reset only sensory-related settings
        highContrastMode = false
        reduceMotion = false
        largeText = false
        contrastLevel = .standard
        reduceRedGreen = false
        reduceBlueYellow = false
        grayscaleMode = false
        textSizeMultiplier = 1.0
        reduceBrightColors = false
        disableFlashing = false
        preferDarkMode = false
        uiSoundVolume = 0.7
        voiceVolume = 0.8
        backgroundMusicVolume = 0.3
        muteAllSounds = false
        reduceSuddenSounds = false
        enableAudioDescriptions = false
        voiceSpeed = .moderate
        voiceType = .gentle
        disableAutoPlay = false
        slowerTransitions = false
        animationSpeed = 1.0
        touchSensitivity = .normal
        hapticFeedback = true
        buttonHoldDelay = false
        preventAccidentalTaps = false
        responseTime = 1.0
        minimizeDistractions = false
        focusMode = false
        simplifiedInterface = false
        visualComplexity = .standard

        saveSettings()
    }

    // MARK: - Preset Application
    func applyPreset(_ preset: SensoryPreset) {
        switch preset {
        case .autism:
            applyAutismPreset()
        case .adhd:
            applyADHDPreset()
        case .dyslexia:
            applyDyslexiaPreset()
        case .visualImpairment:
            applyVisualImpairmentPreset()
        case .hearingImpairment:
            applyHearingImpairmentPreset()
        case .motorImpairment:
            applyMotorImpairmentPreset()
        case .anxiety:
            applyAnxietyPreset()
        case .standard:
            resetToDefaults()
        }
        saveSettings()
    }

    private func applyAutismPreset() {
        // Reduce sensory overload
        reduceMotion = true
        disableAutoPlay = true
        slowerTransitions = true
        muteAllSounds = true
        simplifiedInterface = true
        minimizeDistractions = true
        visualComplexity = .minimal
        breakReminders = true
        reduceBrightColors = true
        disableFlashing = true
        animationSpeed = 0.5
    }

    private func applyADHDPreset() {
        // Minimize distractions
        simplifiedInterface = true
        disableAutoPlay = true
        backgroundMusicVolume = 0.0
        minimizeDistractions = true
        focusMode = true
        breakReminders = true
        visualComplexity = .simple
        reduceSuddenSounds = true
    }

    private func applyDyslexiaPreset() {
        // Reading support
        largeText = true
        textSizeMultiplier = 1.3
        highContrastMode = true
        contrastLevel = .high
        voiceVolume = 0.9
        enableAudioDescriptions = true
        voiceSpeed = .slow
        responseTime = 1.5
    }

    private func applyVisualImpairmentPreset() {
        // Visual accessibility
        highContrastMode = true
        contrastLevel = .maximum
        largeText = true
        textSizeMultiplier = 1.5
        enableAudioDescriptions = true
        voiceVolume = 0.9
        hapticFeedback = true
        touchSensitivity = .light
        responseTime = 2.0
    }

    private func applyHearingImpairmentPreset() {
        // Audio alternatives
        muteAllSounds = true
        enableAudioDescriptions = false
        hapticFeedback = true
        visualComplexity = .rich
        showProgressIndicators = true
        preventAccidentalTaps = true
    }

    private func applyMotorImpairmentPreset() {
        // Motor accessibility
        touchSensitivity = .light
        hapticFeedback = true
        buttonHoldDelay = true
        preventAccidentalTaps = true
        responseTime = 2.5
        slowerTransitions = true
        animationSpeed = 0.7
    }

    private func applyAnxietyPreset() {
        // Calming environment
        reduceMotion = true
        slowerTransitions = true
        backgroundMusicVolume = 0.2
        uiSoundVolume = 0.3
        simplifiedInterface = true
        minimizeDistractions = true
        breakReminders = true
        reduceBrightColors = true
        preferDarkMode = true
        voiceType = .gentle
        voiceSpeed = .slow
    }

    // MARK: - Emergency Features
    func activateCalmMode() {
        // Store current settings
        let originalSettings = getCurrentSettings()

        // Apply calm settings temporarily
        reduceMotion = true
        slowerTransitions = true
        muteAllSounds = true
        simplifiedInterface = true
        minimizeDistractions = true
        reduceBrightColors = true
        backgroundMusicVolume = 0.1

        // Schedule restoration after 5 minutes
        DispatchQueue.main.asyncAfter(deadline: .now() + 300) {
            self.restoreSettings(originalSettings)
        }
    }

    private func getCurrentSettings() -> [String: Any] {
        return [
            "reduceMotion": reduceMotion,
            "slowerTransitions": slowerTransitions,
            "muteAllSounds": muteAllSounds,
            "simplifiedInterface": simplifiedInterface,
            "minimizeDistractions": minimizeDistractions,
            "reduceBrightColors": reduceBrightColors,
            "backgroundMusicVolume": backgroundMusicVolume
        ]
    }

    private func restoreSettings(_ settings: [String: Any]) {
        reduceMotion = settings["reduceMotion"] as? Bool ?? false
        slowerTransitions = settings["slowerTransitions"] as? Bool ?? false
        muteAllSounds = settings["muteAllSounds"] as? Bool ?? false
        simplifiedInterface = settings["simplifiedInterface"] as? Bool ?? false
        minimizeDistractions = settings["minimizeDistractions"] as? Bool ?? false
        reduceBrightColors = settings["reduceBrightColors"] as? Bool ?? false
        backgroundMusicVolume = settings["backgroundMusicVolume"] as? Double ?? 0.3
        saveSettings()
    }

    // MARK: - Grade Management
    func updateGrade(_ newGrade: String) {
        let oldGrade = currentGrade
        currentGrade = newGrade

        // Trigger notification if grade actually changed
        if oldGrade != newGrade {
            gradeChangeNotification = true

            // Reset notification after a brief delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.gradeChangeNotification = false
            }
        }

        saveSettings()
    }
}


