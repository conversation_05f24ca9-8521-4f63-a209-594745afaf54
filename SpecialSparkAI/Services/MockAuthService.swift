import Foundation
import SwiftUI
import SwiftData

// MARK: - Mock Authentication Service
// Provides mock authentication for testing without real Supabase auth

class MockAuthService: ObservableObject {
    static let shared = MockAuthService()

    @Published var isAuthenticated = false
    @Published var currentUser: MockUser?
    @Published var currentStudent: Student?

    private init() {
        // Check if we have a saved mock session
        loadMockSession()
    }

    // MARK: - Mock Authentication Methods

    func signIn(email: String, password: String) async -> Result<MockUser, AuthError> {
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

        // Mock validation
        if email.isEmpty || password.isEmpty {
            return .failure(.invalidCredentials)
        }

        // Create mock user
        let user = MockUser(
            id: UUID(),
            email: email,
            firstName: "Demo",
            lastName: "Student"
        )

        await MainActor.run {
            self.currentUser = user
            self.isAuthenticated = true
            self.createMockStudent(for: user)
            self.saveMockSession()
        }

        return .success(user)
    }

    func signUp(email: String, password: String, firstName: String, lastName: String, gradeLevel: GradeLevel, specialNeeds: [SpecialNeedsType] = [.none]) async -> Result<MockUser, AuthError> {
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds

        // Mock validation
        if email.isEmpty || password.isEmpty || firstName.isEmpty || lastName.isEmpty {
            return .failure(.invalidInput)
        }

        // Create mock user
        let user = MockUser(
            id: UUID(),
            email: email,
            firstName: firstName,
            lastName: lastName
        )

        // Create student profile
        let student = Student(
            firstName: firstName,
            lastName: lastName,
            gradeLevel: gradeLevel,
            specialNeeds: specialNeeds
        )
        student.userId = user.id

        await MainActor.run {
            self.currentUser = user
            self.currentStudent = student
            self.isAuthenticated = true
            self.saveMockSession()
        }

        return .success(user)
    }

    func signOut() {
        currentUser = nil
        currentStudent = nil
        isAuthenticated = false
        clearMockSession()
    }

    func updateStudentProfile(gradeLevel: GradeLevel, specialNeeds: [SpecialNeedsType]) {
        guard let student = currentStudent else { return }

        student.gradeLevel = gradeLevel
        student.schoolLevel = gradeLevel.schoolLevel
        student.specialNeeds = specialNeeds

        saveMockSession()
    }

    // MARK: - Mock Session Management

    private func saveMockSession() {
        if let user = currentUser {
            let userData = try? JSONEncoder().encode(user)
            UserDefaults.standard.set(userData, forKey: "mockUser")
        }

        if let student = currentStudent {
            let studentData = try? JSONEncoder().encode(MockStudentData(from: student))
            UserDefaults.standard.set(studentData, forKey: "mockStudent")
        }

        UserDefaults.standard.set(isAuthenticated, forKey: "isAuthenticated")
    }

    private func loadMockSession() {
        isAuthenticated = UserDefaults.standard.bool(forKey: "isAuthenticated")

        if let userData = UserDefaults.standard.data(forKey: "mockUser"),
           let user = try? JSONDecoder().decode(MockUser.self, from: userData) {
            currentUser = user
        }

        if let studentData = UserDefaults.standard.data(forKey: "mockStudent"),
           let mockStudentData = try? JSONDecoder().decode(MockStudentData.self, from: studentData) {
            currentStudent = mockStudentData.toStudent()
        }
    }

    private func clearMockSession() {
        UserDefaults.standard.removeObject(forKey: "mockUser")
        UserDefaults.standard.removeObject(forKey: "mockStudent")
        UserDefaults.standard.removeObject(forKey: "isAuthenticated")
    }

    private func createMockStudent(for user: MockUser) {
        // Create a default student profile for demo
        let student = Student(
            firstName: user.firstName,
            lastName: user.lastName,
            gradeLevel: .grade5, // Default to 5th grade
            specialNeeds: [.none]
        )
        student.userId = user.id
        currentStudent = student
    }

    // MARK: - Demo Data Methods

    func createDemoProfiles() -> [DemoProfile] {
        return [
            DemoProfile(
                name: "Emma (Kindergarten)",
                gradeLevel: .kindergarten,
                specialNeeds: [.none],
                description: "Kindergarten student ready to learn!"
            ),
            DemoProfile(
                name: "Alex (3rd Grade, ADHD)",
                gradeLevel: .grade3,
                specialNeeds: [.adhd],
                description: "3rd grader with ADHD who loves science"
            ),
            DemoProfile(
                name: "Jordan (6th Grade)",
                gradeLevel: .grade6,
                specialNeeds: [.none],
                description: "Middle school student exploring new subjects"
            ),
            DemoProfile(
                name: "Sam (9th Grade, Autism)",
                gradeLevel: .grade9,
                specialNeeds: [.autism],
                description: "High school freshman on the autism spectrum"
            ),
            DemoProfile(
                name: "Taylor (12th Grade, AP Student)",
                gradeLevel: .grade12,
                specialNeeds: [.none],
                description: "Senior taking AP courses for college prep"
            )
        ]
    }

    func signInWithDemoProfile(_ profile: DemoProfile) async {
        let user = MockUser(
            id: UUID(),
            email: "<EMAIL>",
            firstName: profile.name.components(separatedBy: " ").first ?? "Demo",
            lastName: "Student"
        )

        let student = Student(
            firstName: user.firstName,
            lastName: user.lastName,
            gradeLevel: profile.gradeLevel,
            specialNeeds: profile.specialNeeds
        )
        student.userId = user.id

        await MainActor.run {
            self.currentUser = user
            self.currentStudent = student
            self.isAuthenticated = true
            self.saveMockSession()
        }
    }
}

// MARK: - Supporting Models

struct MockUser: Codable {
    let id: UUID
    let email: String
    let firstName: String
    let lastName: String
    let createdAt: Date

    init(id: UUID, email: String, firstName: String, lastName: String) {
        self.id = id
        self.email = email
        self.firstName = firstName
        self.lastName = lastName
        self.createdAt = Date()
    }

    var fullName: String {
        return "\(firstName) \(lastName)"
    }
}

struct MockStudentData: Codable {
    let id: UUID
    let userId: UUID?
    let firstName: String
    let lastName: String
    let gradeLevel: String
    let specialNeeds: [String]
    let parentEmail: String
    let parentPhone: String

    init(from student: Student) {
        self.id = student.id
        self.userId = student.userId
        self.firstName = student.firstName
        self.lastName = student.lastName
        self.gradeLevel = student.gradeLevel.rawValue
        self.specialNeeds = student.specialNeeds.map { $0.rawValue }
        self.parentEmail = student.parentEmail
        self.parentPhone = student.parentPhone
    }

    func toStudent() -> Student {
        let student = Student(
            firstName: firstName,
            lastName: lastName,
            gradeLevel: GradeLevel(rawValue: gradeLevel) ?? .grade1,
            specialNeeds: specialNeeds.compactMap { SpecialNeedsType(rawValue: $0) },
            parentEmail: parentEmail,
            parentPhone: parentPhone
        )
        student.id = id
        student.userId = userId
        return student
    }
}

struct DemoProfile {
    let name: String
    let gradeLevel: GradeLevel
    let specialNeeds: [SpecialNeedsType]
    let description: String
}

enum AuthError: Error, LocalizedError {
    case invalidCredentials
    case invalidInput
    case networkError
    case unknownError

    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "Invalid email or password"
        case .invalidInput:
            return "Please fill in all required fields"
        case .networkError:
            return "Network connection error"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}
