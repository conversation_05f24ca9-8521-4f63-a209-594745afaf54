//
//  SocialFeaturesService.swift
//  SpecialSparkAI
//
//  Phase 4: Safe Social Features Implementation
//

import Foundation
import SwiftUI

@MainActor
class SocialFeaturesService: ObservableObject {
    static let shared = SocialFeaturesService()

    @Published var studyGroups: [StudyGroup] = []
    @Published var peerConnections: [PeerConnection] = []
    @Published var collaborativeActivities: [CollaborativeActivity] = []
    @Published var safeMessages: [SafeMessage] = []
    @Published var moderationAlerts: [ModerationAlert] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let supabaseService = SupabaseService.shared
    private let mockAuthService = MockAuthService.shared
    private let contentModerationService = ContentModerationService()

    private init() {
        loadSocialData()
    }

    // MARK: - Study Groups Management

    func createStudyGroup(
        name: String,
        subject: String,
        gradeLevel: GradeLevel,
        maxMembers: Int = 6,
        description: String
    ) async -> StudyGroup? {
        guard let currentStudent = mockAuthService.currentStudent else { return nil }

        isLoading = true
        defer { isLoading = false }

        let studyGroup = StudyGroup(
            id: UUID(),
            name: name,
            description: description,
            subject: subject,
            gradeLevel: gradeLevel,
            creatorId: currentStudent.id,
            memberIds: [currentStudent.id],
            maxMembers: maxMembers,
            isActive: true,
            safetySettings: createDefaultSafetySettings(),
            activities: [],
            createdAt: Date().formatted(),
            lastActivity: Date().formatted()
        )

        studyGroups.append(studyGroup)

        // Save to backend
        do {
            try await supabaseService.saveStudyGroup(studyGroup)
            return studyGroup
        } catch {
            errorMessage = "Failed to create study group: \(error.localizedDescription)"
            return nil
        }
    }

    func joinStudyGroup(_ groupId: UUID) async -> Bool {
        guard let currentStudent = mockAuthService.currentStudent,
              let groupIndex = studyGroups.firstIndex(where: { $0.id == groupId }) else { return false }

        var group = studyGroups[groupIndex]

        // Check if already a member
        if group.memberIds.contains(currentStudent.id) { return true }

        // Check if group is full
        if group.memberIds.count >= group.maxMembers { return false }

        // Add student to group
        group.memberIds.append(currentStudent.id)
        studyGroups[groupIndex] = group

        // Create peer connections with existing members
        for memberId in group.memberIds {
            if memberId != currentStudent.id {
                await createPeerConnection(with: memberId, in: groupId)
            }
        }

        return true
    }

    func leaveStudyGroup(_ groupId: UUID) async -> Bool {
        guard let currentStudent = mockAuthService.currentStudent,
              let groupIndex = studyGroups.firstIndex(where: { $0.id == groupId }) else { return false }

        var group = studyGroups[groupIndex]
        group.memberIds.removeAll { $0 == currentStudent.id }

        // If creator leaves and group has other members, transfer ownership
        if group.creatorId == currentStudent.id && !group.memberIds.isEmpty {
            group.creatorId = group.memberIds.first!
        }

        // If no members left, deactivate group
        if group.memberIds.isEmpty {
            group.isActive = false
        }

        studyGroups[groupIndex] = group

        // Remove peer connections related to this group
        peerConnections.removeAll { connection in
            connection.contextId == groupId &&
            (connection.studentId == currentStudent.id || connection.peerId == currentStudent.id)
        }

        return true
    }

    // MARK: - Peer Connections

    private func createPeerConnection(with peerId: UUID, in contextId: UUID) async {
        guard let currentStudent = mockAuthService.currentStudent else { return }

        // Check if connection already exists
        let existingConnection = peerConnections.first { connection in
            (connection.studentId == currentStudent.id && connection.peerId == peerId) ||
            (connection.studentId == peerId && connection.peerId == currentStudent.id)
        }

        if existingConnection != nil { return }

        let connection = PeerConnection(
            id: UUID(),
            studentId: currentStudent.id,
            peerId: peerId,
            connectionType: .studyBuddy,
            status: .pending,
            contextId: contextId,
            contextType: .studyGroup,
            safetyLevel: PeerSafetyLevel.supervised,
            permissions: createDefaultPeerPermissions(),
            createdAt: Date().formatted(),
            lastInteraction: Date().formatted()
        )

        peerConnections.append(connection)
    }

    func acceptPeerConnection(_ connectionId: UUID) async -> Bool {
        guard let connectionIndex = peerConnections.firstIndex(where: { $0.id == connectionId }) else { return false }

        var connection = peerConnections[connectionIndex]
        connection.status = .active
        connection.lastInteraction = Date().formatted()

        peerConnections[connectionIndex] = connection
        return true
    }

    func blockPeerConnection(_ connectionId: UUID) async -> Bool {
        guard let connectionIndex = peerConnections.firstIndex(where: { $0.id == connectionId }) else { return false }

        var connection = peerConnections[connectionIndex]
        connection.status = .blocked
        connection.lastInteraction = Date().formatted()

        peerConnections[connectionIndex] = connection

        // Create moderation alert
        await createModerationAlert(
            type: .peerBlocked,
            description: "Student blocked peer connection",
            severity: .medium,
            relatedId: connectionId
        )

        return true
    }

    // MARK: - Collaborative Activities

    func createCollaborativeActivity(
        title: String,
        description: String,
        type: CollaborativeActivityType,
        subject: String,
        studyGroupId: UUID,
        maxParticipants: Int = 4
    ) async -> CollaborativeActivity? {
        guard let currentStudent = mockAuthService.currentStudent else { return nil }

        let activity = CollaborativeActivity(
            id: UUID(),
            title: title,
            description: description,
            type: type,
            subject: subject,
            creatorId: currentStudent.id,
            studyGroupId: studyGroupId,
            participantIds: [currentStudent.id],
            maxParticipants: maxParticipants,
            status: .active,
            safetySettings: createDefaultActivitySafetySettings(),
            content: CollaborativeContent(
                sharedDocuments: [],
                chatMessages: [],
                submissions: [],
                resources: []
            ),
            createdAt: Date().formatted(),
            lastActivity: Date().formatted()
        )

        collaborativeActivities.append(activity)
        return activity
    }

    func joinCollaborativeActivity(_ activityId: UUID) async -> Bool {
        guard let currentStudent = mockAuthService.currentStudent,
              let activityIndex = collaborativeActivities.firstIndex(where: { $0.id == activityId }) else { return false }

        var activity = collaborativeActivities[activityIndex]

        // Check if already participating
        if activity.participantIds.contains(currentStudent.id) { return true }

        // Check if activity is full
        if activity.participantIds.count >= activity.maxParticipants { return false }

        activity.participantIds.append(currentStudent.id)
        activity.lastActivity = Date().formatted()

        collaborativeActivities[activityIndex] = activity
        return true
    }

    // MARK: - Safe Messaging

    func sendSafeMessage(
        to recipientId: UUID,
        content: String,
        contextId: UUID,
        contextType: MessageContextType
    ) async -> Bool {
        guard let currentStudent = mockAuthService.currentStudent else { return false }

        // Content moderation check
        let moderationResult = await contentModerationService.moderateContent(content)

        if !moderationResult.isApproved {
            await createModerationAlert(
                type: .inappropriateContent,
                description: "Message blocked due to inappropriate content",
                severity: .high,
                relatedId: currentStudent.id
            )
            return false
        }

        let message = SafeMessage(
            id: UUID(),
            senderId: currentStudent.id,
            recipientId: recipientId,
            content: content,
            contextId: contextId,
            contextType: contextType,
            messageType: .text,
            status: .sent,
            moderationStatus: .approved,
            safetyFlags: [],
            timestamp: Date().formatted(),
            readAt: nil
        )

        safeMessages.append(message)

        // Auto-moderate and flag if needed
        await performAutoModeration(message)

        return true
    }

    func reportMessage(_ messageId: UUID, reason: String) async {
        guard let messageIndex = safeMessages.firstIndex(where: { $0.id == messageId }) else { return }

        var message = safeMessages[messageIndex]
        message.safetyFlags.append(SafetyFlag(
            id: UUID(),
            type: .reported,
            reason: reason,
            reporterId: mockAuthService.currentStudent?.id,
            timestamp: Date().formatted(),
            severity: .medium
        ))

        safeMessages[messageIndex] = message

        // Create moderation alert
        await createModerationAlert(
            type: .messageReported,
            description: "Message reported: \(reason)",
            severity: .high,
            relatedId: messageId
        )
    }

    // MARK: - Content Moderation

    private func performAutoModeration(_ message: SafeMessage) async {
        // Implement AI-powered content analysis
        let riskScore = await contentModerationService.calculateRiskScore(message.content)

        if riskScore > 0.7 {
            await createModerationAlert(
                type: .highRiskContent,
                description: "High-risk content detected in message",
                severity: .critical,
                relatedId: message.id
            )
        }
    }

    private func createModerationAlert(
        type: ModerationType,
        description: String,
        severity: ModerationSeverity,
        relatedId: UUID
    ) async {
        let alert = ModerationAlert(
            id: UUID(),
            type: type,
            description: description,
            severity: severity,
            relatedId: relatedId,
            reporterId: mockAuthService.currentStudent?.id,
            status: .pending,
            createdAt: Date().formatted(),
            resolvedAt: nil,
            moderatorNotes: []
        )

        moderationAlerts.append(alert)
    }

    // MARK: - Safety Settings

    private func createDefaultSafetySettings() -> SafetySettings {
        return SafetySettings(
            contentModeration: .strict,
            allowDirectMessages: false,
            allowFileSharing: false,
            allowVideoChat: false,
            allowVoiceChat: false,
            parentalSupervision: .required,
            moderatorApproval: .required,
            timeRestrictions: TimeRestrictions(
                allowedHours: 9...17, // 9 AM to 5 PM
                allowedDays: [.monday, .tuesday, .wednesday, .thursday, .friday],
                maxSessionDuration: 60 // minutes
            )
        )
    }

    private func createDefaultPeerPermissions() -> PeerPermissions {
        return PeerPermissions(
            canSendMessages: true,
            canShareFiles: false,
            canInitiateVideoCall: false,
            canInitiateVoiceCall: false,
            canViewProfile: true,
            canInviteToActivities: true,
            canCollaborate: true
        )
    }

    private func createDefaultActivitySafetySettings() -> ActivitySafetySettings {
        return ActivitySafetySettings(
            requiresModeration: true,
            allowsFileSharing: false,
            allowsExternalLinks: false,
            contentFiltering: .strict,
            participantVerification: .required,
            timeLimit: 60 // minutes
        )
    }

    // MARK: - Data Loading

    private func loadSocialData() {
        // Load sample data
        loadSampleStudyGroups()
        loadSampleCollaborativeActivities()
    }

    private func loadSampleStudyGroups() {
        studyGroups = [
            StudyGroup(
                id: UUID(),
                name: "Math Explorers",
                description: "Fun math activities and problem solving",
                subject: "Mathematics",
                gradeLevel: .grade3,
                creatorId: UUID(),
                memberIds: [UUID(), UUID(), UUID()],
                maxMembers: 6,
                isActive: true,
                safetySettings: createDefaultSafetySettings(),
                activities: [],
                createdAt: Date().formatted(),
                lastActivity: Date().formatted()
            ),
            StudyGroup(
                id: UUID(),
                name: "Reading Circle",
                description: "Share stories and improve reading skills",
                subject: "Reading",
                gradeLevel: .grade2,
                creatorId: UUID(),
                memberIds: [UUID(), UUID()],
                maxMembers: 4,
                isActive: true,
                safetySettings: createDefaultSafetySettings(),
                activities: [],
                createdAt: Date().formatted(),
                lastActivity: Date().formatted()
            )
        ]
    }

    private func loadSampleCollaborativeActivities() {
        collaborativeActivities = [
            CollaborativeActivity(
                id: UUID(),
                title: "Science Fair Project",
                description: "Work together on a volcano experiment",
                type: .project,
                subject: "Science",
                creatorId: UUID(),
                studyGroupId: studyGroups.first?.id ?? UUID(),
                participantIds: [UUID(), UUID()],
                maxParticipants: 4,
                status: .active,
                safetySettings: createDefaultActivitySafetySettings(),
                content: CollaborativeContent(
                    sharedDocuments: [],
                    chatMessages: [],
                    submissions: [],
                    resources: []
                ),
                createdAt: Date().formatted(),
                lastActivity: Date().formatted()
            )
        ]
    }
}

// MARK: - Content Moderation Service

class ContentModerationService {
    func moderateContent(_ content: String) async -> ModerationResult {
        // Implement AI-powered content moderation
        // This would integrate with services like OpenAI Moderation API

        let inappropriateWords = ["bad", "mean", "stupid"] // Simplified example
        let containsInappropriate = inappropriateWords.contains { content.lowercased().contains($0) }

        return ModerationResult(
            isApproved: !containsInappropriate,
            riskScore: containsInappropriate ? 0.8 : 0.1,
            flags: containsInappropriate ? [.inappropriateLanguage] : [],
            suggestedAction: containsInappropriate ? .block : .approve
        )
    }

    func calculateRiskScore(_ content: String) async -> Double {
        // Calculate risk score based on content analysis
        return content.lowercased().contains("bad") ? 0.8 : 0.2
    }
}

struct ModerationResult {
    let isApproved: Bool
    let riskScore: Double
    let flags: [ModerationFlag]
    let suggestedAction: ModerationAction
}

enum ModerationFlag {
    case inappropriateLanguage
    case personalInformation
    case externalLink
    case spam
}

enum ModerationAction {
    case approve
    case block
    case review
    case modify
}
