import Foundation
import SwiftData

// MARK: - Subject Data Service
// Provides complete K-12 curriculum subjects

class SubjectDataService {
    static let shared = SubjectDataService()

    private init() {}

    // MARK: - Get Subjects by Grade Level

    func getSubjectsFor(gradeLevel: GradeLevel) -> [Subject] {
        return allSubjects.filter { subject in
            subject.isAvailableFor(gradeLevel: gradeLevel)
        }
    }

    func getSubjectsFor(schoolLevel: SchoolLevel) -> [Subject] {
        return allSubjects.filter { subject in
            subject.isAvailableFor(schoolLevel: schoolLevel)
        }
    }

    func getCoreSubjectsFor(gradeLevel: GradeLevel) -> [Subject] {
        return getSubjectsFor(gradeLevel: gradeLevel).filter { $0.category == .core }
    }

    func getAPSubjectsFor(gradeLevel: GradeLevel) -> [Subject] {
        return getSubjectsFor(gradeLevel: gradeLevel).filter { $0.isAP }
    }

    func getSpecialNeedsSubjects() -> [Subject] {
        return allSubjects.filter { $0.category == .specialNeeds }
    }

    // MARK: - Complete K-12 Subject Catalog

    lazy var allSubjects: [Subject] = [
        // MARK: - Elementary Subjects (K-5)

        // Core Elementary
        Subject(name: "Reading & Language Arts", code: "ELA_ELEM", category: .core,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Foundational reading, writing, and language skills"),

        Subject(name: "Mathematics", code: "MATH_ELEM", category: .core,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Basic math concepts and problem solving"),

        Subject(name: "Science", code: "SCI_ELEM", category: .stem,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Introduction to scientific concepts and inquiry"),

        Subject(name: "Social Studies", code: "SS_ELEM", category: .socialStudies,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Community, geography, and basic history"),

        // MARK: - Middle School Subjects (6-8)

        // Core Middle School
        Subject(name: "English Language Arts", code: "ELA_MID", category: .core,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7, .grade8],
                description: "Advanced reading, writing, and literature"),

        Subject(name: "Pre-Algebra", code: "PREALG", category: .core,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7],
                description: "Introduction to algebraic concepts"),

        Subject(name: "Algebra I", code: "ALG1", category: .core,
                schoolLevels: [.middle, .high], gradeLevels: [.grade8, .grade9],
                description: "Linear equations and functions"),

        Subject(name: "Life Science", code: "LIFESCI", category: .stem,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7],
                description: "Biology basics and life processes"),

        Subject(name: "Physical Science", code: "PHYSCI", category: .stem,
                schoolLevels: [.middle], gradeLevels: [.grade8],
                description: "Introduction to chemistry and physics"),

        Subject(name: "World History", code: "WHIST_MID", category: .socialStudies,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7],
                description: "Ancient and medieval civilizations"),

        Subject(name: "US History", code: "USHIST_MID", category: .socialStudies,
                schoolLevels: [.middle], gradeLevels: [.grade8],
                description: "American history through Civil War"),

        // MARK: - High School Core Subjects (9-12)

        // English
        Subject(name: "English 9", code: "ENG9", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade9],
                description: "Literature and composition"),

        Subject(name: "English 10", code: "ENG10", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade10],
                description: "World literature and writing"),

        Subject(name: "English 11", code: "ENG11", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade11],
                description: "American literature"),

        Subject(name: "English 12", code: "ENG12", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "British literature and college prep"),

        // Mathematics
        Subject(name: "Geometry", code: "GEOM", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Geometric proofs and spatial reasoning"),

        Subject(name: "Algebra II", code: "ALG2", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "Advanced algebraic concepts"),

        Subject(name: "Pre-Calculus", code: "PRECALC", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Trigonometry and advanced functions"),

        Subject(name: "Statistics", code: "STATS", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Data analysis and probability"),

        // Sciences
        Subject(name: "Biology", code: "BIO", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Cellular biology and genetics"),

        Subject(name: "Chemistry", code: "CHEM", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "Chemical reactions and bonding"),

        Subject(name: "Physics", code: "PHYS", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Mechanics and electromagnetic theory"),

        Subject(name: "Environmental Science", code: "ENVSCI", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Ecology and environmental systems"),

        // Social Studies
        Subject(name: "World History", code: "WHIST_HIGH", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade9],
                description: "Global civilizations and cultures"),

        Subject(name: "US History", code: "USHIST_HIGH", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "American history and government"),

        Subject(name: "Government", code: "GOV", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "Civics and political systems"),

        Subject(name: "Economics", code: "ECON", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "Economic principles and systems"),

        // MARK: - Advanced Placement (AP) Subjects

        Subject(name: "AP English Literature", code: "AP_ENGLIT", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level literature analysis", isAP: true),

        Subject(name: "AP English Language", code: "AP_ENGLANG", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level composition and rhetoric", isAP: true),

        Subject(name: "AP Calculus AB", code: "AP_CALCAB", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Differential and integral calculus", isAP: true),

        Subject(name: "AP Calculus BC", code: "AP_CALCBC", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "Advanced calculus concepts", isAP: true),

        Subject(name: "AP Biology", code: "AP_BIO", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level biology", isAP: true),

        Subject(name: "AP Chemistry", code: "AP_CHEM", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level chemistry", isAP: true),

        Subject(name: "AP Physics 1", code: "AP_PHYS1", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Algebra-based physics", isAP: true),

        Subject(name: "AP US History", code: "AP_USHIST", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "College-level American history", isAP: true),

        Subject(name: "AP World History", code: "AP_WHIST", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade10],
                description: "College-level world history", isAP: true),

        Subject(name: "AP Psychology", code: "AP_PSYCH", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Introduction to psychological science", isAP: true),

        // MARK: - World Languages

        Subject(name: "Spanish I", code: "SPAN1", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Beginning Spanish language"),

        Subject(name: "Spanish II", code: "SPAN2", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10],
                description: "Intermediate Spanish language"),

        Subject(name: "Spanish III", code: "SPAN3", category: .language,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "Advanced Spanish language"),

        Subject(name: "French I", code: "FREN1", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Beginning French language"),

        Subject(name: "French II", code: "FREN2", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10],
                description: "Intermediate French language"),

        // MARK: - Arts & Electives

        Subject(name: "Art", code: "ART", category: .arts,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Visual arts and creativity"),

        Subject(name: "Music", code: "MUSIC", category: .arts,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Music theory and performance"),

        Subject(name: "Physical Education", code: "PE", category: .physicalEducation,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Physical fitness and health"),

        // MARK: - Special Needs Support

        Subject(name: "Speech Therapy", code: "SPEECH", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Communication skills development"),

        Subject(name: "Occupational Therapy", code: "OT", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Daily living skills and motor development"),

        Subject(name: "Social Skills", code: "SOCIAL", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Social interaction and communication"),

        Subject(name: "Life Skills", code: "LIFESKILLS", category: .lifeSkills,
                schoolLevels: [.middle, .high],
                gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Independent living and practical skills"),

        // MARK: - Computer Science & Technology (K-12)

        Subject(name: "Digital Literacy", code: "DIGLIT_ELEM", category: .technology,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Basic computer skills and digital citizenship"),

        Subject(name: "Coding Basics", code: "CODE_ELEM", category: .technology,
                schoolLevels: [.elementary], gradeLevels: [.grade3, .grade4, .grade5],
                description: "Introduction to programming with visual tools"),

        Subject(name: "Computer Science", code: "CS_MID", category: .technology,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7, .grade8],
                description: "Programming fundamentals and computational thinking"),

        Subject(name: "Web Development", code: "WEBDEV", category: .technology,
                schoolLevels: [.middle, .high], gradeLevels: [.grade8, .grade9, .grade10, .grade11],
                description: "HTML, CSS, JavaScript and web design"),

        Subject(name: "Computer Science Principles", code: "CSP", category: .technology,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Broad introduction to computer science concepts"),

        Subject(name: "Computer Programming", code: "PROG", category: .technology,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Advanced programming in multiple languages"),

        Subject(name: "Data Science", code: "DATASCI", category: .technology,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Data analysis, statistics, and machine learning"),

        Subject(name: "Cybersecurity", code: "CYBER", category: .technology,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Network security and digital safety"),

        Subject(name: "Robotics", code: "ROBOT", category: .technology,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Building and programming robots"),

        Subject(name: "Game Development", code: "GAMEDEV", category: .technology,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Creating video games and interactive media"),

        // MARK: - Advanced AP Computer Science

        Subject(name: "AP Computer Science Principles", code: "AP_CSP", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "College-level computer science concepts", isAP: true),

        Subject(name: "AP Computer Science A", code: "AP_CSA", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Advanced programming in Java", isAP: true),

        // MARK: - Business & Entrepreneurship

        Subject(name: "Business Basics", code: "BIZ_ELEM", category: .business,
                schoolLevels: [.elementary], gradeLevels: [.grade4, .grade5],
                description: "Introduction to money, saving, and entrepreneurship"),

        Subject(name: "Financial Literacy", code: "FINLIT", category: .business,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Personal finance and money management"),

        Subject(name: "Entrepreneurship", code: "ENTRE", category: .business,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Starting and running a business"),

        Subject(name: "Business Management", code: "BIZMAN", category: .business,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Leadership and business operations"),

        Subject(name: "Marketing", code: "MARKET", category: .business,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Advertising and consumer behavior"),

        Subject(name: "Accounting", code: "ACCT", category: .business,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Financial record keeping and analysis"),

        // MARK: - Advanced Arts & Creative

        Subject(name: "Drama/Theater", code: "DRAMA", category: .arts,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Acting, stagecraft, and theatrical performance"),

        Subject(name: "Digital Arts", code: "DIGART", category: .arts,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Computer graphics and digital design"),

        Subject(name: "Photography", code: "PHOTO", category: .arts,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Visual storytelling through photography"),

        Subject(name: "Film & Video Production", code: "FILM", category: .arts,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10, .grade11, .grade12],
                description: "Creating and editing video content"),

        Subject(name: "Creative Writing", code: "CRWRITE", category: .arts,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Poetry, fiction, and creative expression"),

        Subject(name: "Journalism", code: "JOURN", category: .arts,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10, .grade11, .grade12],
                description: "News writing and media production"),

        Subject(name: "Graphic Design", code: "GDESIGN", category: .arts,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Visual communication and design principles"),

        // MARK: - Career & Technical Education

        Subject(name: "Career Exploration", code: "CAREER", category: .careerTech,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Exploring different career paths and skills"),

        Subject(name: "Health Sciences", code: "HEALTHSCI", category: .careerTech,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Medical and healthcare career preparation"),

        Subject(name: "Engineering Design", code: "ENGDES", category: .careerTech,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10, .grade11, .grade12],
                description: "Engineering principles and design thinking"),

        Subject(name: "Automotive Technology", code: "AUTO", category: .careerTech,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Vehicle maintenance and repair"),

        Subject(name: "Culinary Arts", code: "CULINARY", category: .careerTech,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Cooking and food service management"),

        Subject(name: "Construction Technology", code: "CONSTRUCT", category: .careerTech,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11, .grade12],
                description: "Building trades and construction skills"),

        Subject(name: "Fashion Design", code: "FASHION", category: .careerTech,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10, .grade11, .grade12],
                description: "Clothing design and textile arts"),

        // MARK: - Additional World Languages

        Subject(name: "German I", code: "GER1", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Beginning German language"),

        Subject(name: "German II", code: "GER2", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10],
                description: "Intermediate German language"),

        Subject(name: "Mandarin Chinese I", code: "CHIN1", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Beginning Mandarin Chinese"),

        Subject(name: "Mandarin Chinese II", code: "CHIN2", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10],
                description: "Intermediate Mandarin Chinese"),

        Subject(name: "Italian I", code: "ITAL1", category: .language,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Beginning Italian language"),

        Subject(name: "Japanese I", code: "JPN1", category: .language,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Beginning Japanese language"),

        Subject(name: "Latin I", code: "LAT1", category: .language,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Classical Latin language and culture"),

        // MARK: - Additional AP Courses

        Subject(name: "AP Statistics", code: "AP_STATS", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level statistics and data analysis", isAP: true),

        Subject(name: "AP Environmental Science", code: "AP_ENVSCI", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level environmental science", isAP: true),

        Subject(name: "AP Human Geography", code: "AP_HUMGEO", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "College-level human geography", isAP: true),

        Subject(name: "AP European History", code: "AP_EURHIST", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "College-level European history", isAP: true),

        Subject(name: "AP Government & Politics", code: "AP_GOV", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "College-level government and politics", isAP: true),

        Subject(name: "AP Macroeconomics", code: "AP_MACRO", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "College-level macroeconomics", isAP: true),

        Subject(name: "AP Microeconomics", code: "AP_MICRO", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "College-level microeconomics", isAP: true),

        Subject(name: "AP Art History", code: "AP_ARTHIST", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level art history", isAP: true),

        Subject(name: "AP Music Theory", code: "AP_MUSIC", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level music theory", isAP: true),

        Subject(name: "AP Spanish Language", code: "AP_SPAN", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level Spanish language", isAP: true),

        Subject(name: "AP French Language", code: "AP_FREN", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level French language", isAP: true),

        // MARK: - Dual Enrollment & College Prep

        Subject(name: "College Algebra", code: "COLL_ALG", category: .dualEnrollment,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level algebra course"),

        Subject(name: "College English Composition", code: "COLL_ENG", category: .dualEnrollment,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level writing and composition"),

        Subject(name: "Introduction to Psychology", code: "COLL_PSYCH", category: .dualEnrollment,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level psychology introduction"),

        Subject(name: "Introduction to Sociology", code: "COLL_SOC", category: .dualEnrollment,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level sociology introduction"),

        Subject(name: "College Prep", code: "COLLPREP", category: .collegePrep,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "SAT/ACT preparation and college readiness"),

        Subject(name: "Study Skills", code: "STUDYSKILLS", category: .collegePrep,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Learning strategies and academic success skills"),

        // MARK: - Health & Wellness

        Subject(name: "Health Education", code: "HEALTH", category: .health,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Personal health and wellness"),

        Subject(name: "Nutrition", code: "NUTRITION", category: .health,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Food science and healthy eating"),

        Subject(name: "Mental Health & Wellness", code: "MENTAL", category: .health,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Emotional well-being and mental health"),

        Subject(name: "First Aid & Safety", code: "FIRSTAID", category: .health,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Emergency response and safety skills"),

        // MARK: - Advanced Special Needs Support

        Subject(name: "Adaptive Physical Education", code: "APE", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Modified physical education for special needs"),

        Subject(name: "Assistive Technology", code: "ASSISTTECH", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Technology tools for learning support"),

        Subject(name: "Transition Planning", code: "TRANSITION", category: .specialNeeds,
                schoolLevels: [.high],
                gradeLevels: [.grade9, .grade10, .grade11, .grade12],
                description: "Post-secondary life and career planning"),

        Subject(name: "Sensory Integration", code: "SENSORY", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Sensory processing and integration support"),

        Subject(name: "Behavioral Support", code: "BEHAVIOR", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Positive behavior intervention and support")
    ]
}
