-- SpecialSpark AI Database Schema
-- Complete database setup for virtual school platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Student Profiles Table
CREATE TABLE IF NOT EXISTS student_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    grade_level VARCHAR(20) NOT NULL,
    school_level VARCHAR(20) NOT NULL,
    profile_image_url TEXT,
    parent_email VARCHAR(255),
    special_needs TEXT[] DEFAULT '{}',
    learning_style VARCHAR(50) DEFAULT 'visual',
    preferred_language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Teacher Agents Table
CREATE TABLE IF NOT EXISTS ai_teacher_agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    role VARCHAR(100) NOT NULL,
    specialization VARCHAR(100) NOT NULL,
    grade_level VARCHAR(20) NOT NULL,
    personality JSONB NOT NULL,
    avatar_url TEXT,
    capabilities TEXT[] DEFAULT '{}',
    adaptation_strategies TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subjects Table
CREATE TABLE IF NOT EXISTS subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    grade_level VARCHAR(20) NOT NULL,
    school_level VARCHAR(20) NOT NULL,
    skills TEXT[] DEFAULT '{}',
    prerequisites TEXT[] DEFAULT '{}',
    learning_objectives TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Sessions Table
CREATE TABLE IF NOT EXISTS learning_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    subject_id UUID NOT NULL REFERENCES subjects(id) ON DELETE CASCADE,
    teacher_agent_id UUID NOT NULL REFERENCES ai_teacher_agents(id) ON DELETE CASCADE,
    session_type VARCHAR(50) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in seconds
    topics_covered TEXT[] DEFAULT '{}',
    difficulty_level VARCHAR(20) NOT NULL,
    completion_rate DECIMAL(5,2) DEFAULT 0.0,
    engagement_score DECIMAL(5,2) DEFAULT 0.0,
    emotional_state VARCHAR(50),
    adaptations_used TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Progress Table
CREATE TABLE IF NOT EXISTS learning_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    subject_id UUID NOT NULL REFERENCES subjects(id) ON DELETE CASCADE,
    skill_id UUID NOT NULL,
    current_level VARCHAR(20) NOT NULL,
    mastery_score DECIMAL(5,2) DEFAULT 0.0,
    attempts_count INTEGER DEFAULT 0,
    last_attempt_date TIMESTAMP WITH TIME ZONE,
    strength_areas TEXT[] DEFAULT '{}',
    improvement_areas TEXT[] DEFAULT '{}',
    next_recommendations TEXT[] DEFAULT '{}',
    adaptive_path VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, subject_id, skill_id)
);

-- Assessment Records Table
CREATE TABLE IF NOT EXISTS assessment_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    session_id UUID NOT NULL REFERENCES learning_sessions(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    question_text TEXT NOT NULL,
    student_answer TEXT NOT NULL,
    correct_answer TEXT NOT NULL,
    is_correct BOOLEAN NOT NULL,
    response_time DECIMAL(8,2), -- in seconds
    hints_used INTEGER DEFAULT 0,
    difficulty_level VARCHAR(20) NOT NULL,
    skill_tested VARCHAR(100) NOT NULL,
    adaptive_adjustment VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Adaptive Learning Recommendations Table
CREATE TABLE IF NOT EXISTS adaptive_learning_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    action_items TEXT[] DEFAULT '{}',
    estimated_duration INTEGER, -- in minutes
    difficulty_adjustment VARCHAR(50),
    personality_adjustment VARCHAR(50),
    is_completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Emotional State Tracking Table
CREATE TABLE IF NOT EXISTS emotional_state_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    session_id UUID REFERENCES learning_sessions(id) ON DELETE CASCADE,
    emotional_state VARCHAR(50) NOT NULL,
    confidence DECIMAL(3,2) DEFAULT 0.0,
    engagement DECIMAL(3,2) DEFAULT 0.0,
    frustration DECIMAL(3,2) DEFAULT 0.0,
    excitement DECIMAL(3,2) DEFAULT 0.0,
    context TEXT,
    triggers TEXT[] DEFAULT '{}',
    interventions TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Parent Dashboard Data View
CREATE OR REPLACE VIEW parent_dashboard_data AS
SELECT
    sp.id as student_id,
    sp.first_name,
    sp.last_name,
    sp.grade_level,
    -- Weekly progress calculation
    COALESCE(
        (SELECT jsonb_object_agg(
            DATE_TRUNC('day', ls.start_time)::date,
            AVG(ls.completion_rate)
        )
        FROM learning_sessions ls
        WHERE ls.student_id = sp.id
        AND ls.start_time >= NOW() - INTERVAL '7 days'
        GROUP BY DATE_TRUNC('day', ls.start_time)
        ), '{}'::jsonb
    ) as weekly_progress,

    -- Subject performance
    COALESCE(
        (SELECT jsonb_object_agg(
            s.name,
            AVG(lp.mastery_score)
        )
        FROM learning_progress lp
        JOIN subjects s ON lp.subject_id = s.id
        WHERE lp.student_id = sp.id
        GROUP BY s.name
        ), '{}'::jsonb
    ) as subject_performance,

    -- Time spent learning this week
    COALESCE(
        (SELECT SUM(duration)
        FROM learning_sessions
        WHERE student_id = sp.id
        AND start_time >= NOW() - INTERVAL '7 days'
        ), 0
    ) as time_spent_learning,

    NOW() as last_updated
FROM student_profiles sp
WHERE sp.is_active = true;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_learning_sessions_student_id ON learning_sessions(student_id);
CREATE INDEX IF NOT EXISTS idx_learning_sessions_start_time ON learning_sessions(start_time);
CREATE INDEX IF NOT EXISTS idx_learning_progress_student_id ON learning_progress(student_id);
CREATE INDEX IF NOT EXISTS idx_assessment_records_student_id ON assessment_records(student_id);
CREATE INDEX IF NOT EXISTS idx_emotional_state_student_id ON emotional_state_records(student_id);

-- Row Level Security Policies
ALTER TABLE student_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE emotional_state_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE adaptive_learning_recommendations ENABLE ROW LEVEL SECURITY;

-- Policies (basic - should be customized based on auth requirements)
CREATE POLICY "Users can view their own student profiles" ON student_profiles
    FOR SELECT USING (auth.uid()::text = parent_email OR auth.uid()::text = id::text);

CREATE POLICY "Users can update their own student profiles" ON student_profiles
    FOR UPDATE USING (auth.uid()::text = parent_email OR auth.uid()::text = id::text);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_student_profiles_updated_at
    BEFORE UPDATE ON student_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_progress_updated_at
    BEFORE UPDATE ON learning_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO subjects (name, description, grade_level, school_level, skills, learning_objectives) VALUES
('Mathematics', 'Core mathematics curriculum', 'Grade 3', 'Elementary',
 ARRAY['Addition', 'Subtraction', 'Multiplication', 'Division', 'Fractions'],
 ARRAY['Master basic arithmetic', 'Understand number relationships', 'Solve word problems']),
('English Language Arts', 'Reading, writing, and language skills', 'Grade 3', 'Elementary',
 ARRAY['Reading Comprehension', 'Vocabulary', 'Grammar', 'Writing', 'Spelling'],
 ARRAY['Read grade-level texts fluently', 'Write clear sentences', 'Expand vocabulary']),
('Science', 'Elementary science exploration', 'Grade 3', 'Elementary',
 ARRAY['Scientific Method', 'Life Science', 'Physical Science', 'Earth Science'],
 ARRAY['Understand scientific inquiry', 'Explore living things', 'Learn about matter and energy']);

-- Insert sample AI teacher agents
INSERT INTO ai_teacher_agents (name, role, specialization, grade_level, personality, capabilities, adaptation_strategies) VALUES
('Ms. Maya', 'Math Teacher', 'Elementary Mathematics', 'Grade 3',
 '{"warmth": 9, "patience": 10, "enthusiasm": 8, "humor": 7, "empathy": 9}',
 ARRAY['Visual explanations', 'Step-by-step guidance', 'Encouraging feedback'],
 ARRAY['Slow down for processing delays', 'Use visual aids', 'Provide extra encouragement']),
('Mr. Alex', 'Reading Teacher', 'Language Arts', 'Grade 3',
 '{"warmth": 8, "patience": 9, "enthusiasm": 9, "humor": 8, "empathy": 8}',
 ARRAY['Interactive storytelling', 'Phonics support', 'Vocabulary building'],
 ARRAY['Adjust reading level', 'Use audio support', 'Break into smaller chunks']),
('Dr. Sam', 'Science Teacher', 'Elementary Science', 'Grade 3',
 '{"warmth": 7, "patience": 8, "enthusiasm": 10, "humor": 9, "empathy": 7}',
 ARRAY['Hands-on experiments', 'Visual demonstrations', 'Real-world connections'],
 ARRAY['Simplify complex concepts', 'Use concrete examples', 'Encourage exploration']);
