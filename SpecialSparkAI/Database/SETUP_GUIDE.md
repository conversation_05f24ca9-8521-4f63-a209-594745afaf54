# SpecialSpark AI - Database Setup Guide

## 🗄️ **Supabase Database Setup**

### **Step 1: Create Supabase Project**

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Create a new project:
   - **Project Name**: `SpecialSpark AI`
   - **Database Password**: Choose a strong password
   - **Region**: Select closest to your location

### **Step 2: Get Your Credentials**

1. Go to **Settings** → **API**
2. Copy these values to your `Config.swift`:
   - **Project URL** → `supabaseURL`
   - **anon public key** → `supabaseAnonKey`

### **Step 3: Run Database Schema**

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy and paste the entire contents of `create_tables.sql`
3. Click **Run** to create all tables

### **Step 4: Verify Setup**

Run this query to verify tables were created:

```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

You should see these tables:
- `student_profiles`
- `ai_teacher_agents`
- `subjects`
- `learning_sessions`
- `learning_progress`
- `assessment_records`
- `adaptive_learning_recommendations`
- `emotional_state_records`

## 🔑 **API Keys Setup**

### **Step 1: Get Gemini API Key**

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key

### **Step 2: Update Config.swift**

Replace the placeholder values in `Config.swift`:

```swift
struct APIConfig {
    static let geminiAPIKey = "YOUR_ACTUAL_GEMINI_API_KEY"
    static let supabaseURL = "YOUR_ACTUAL_SUPABASE_URL"
    static let supabaseAnonKey = "YOUR_ACTUAL_SUPABASE_ANON_KEY"
}
```

## 🧪 **Testing the Setup**

### **Test 1: Database Connection**

1. Build and run the app
2. Check the console for: `✅ Supabase URL loaded from environment`

### **Test 2: AI Agent**

1. Go to the **AI Test** tab in the app
2. Ask a question like: "What is 2 + 2?"
3. Verify you get a response from Gemini

### **Test 3: Adaptive Learning**

1. The app will automatically start tracking learning sessions
2. Check the Supabase dashboard → **Table Editor** → `learning_sessions`
3. You should see data being populated

## 📊 **Sample Data**

The schema includes sample data:
- **3 AI Teacher Agents**: Ms. Maya (Math), Mr. Alex (Reading), Dr. Sam (Science)
- **3 Subjects**: Mathematics, English Language Arts, Science
- **Grade Level**: Grade 3 (easily expandable)

## 🔧 **Troubleshooting**

### **Database Issues**
- Verify your Supabase URL and keys are correct
- Check that RLS (Row Level Security) policies are set up
- Ensure the database schema ran without errors

### **API Issues**
- Verify your Gemini API key is valid
- Check network connectivity
- Look for error messages in the Xcode console

### **Build Issues**
- Clean build folder: **Product** → **Clean Build Folder**
- Restart Xcode
- Check that all dependencies are properly installed

## 🚀 **Next Steps**

Once setup is complete:

1. **Add Students**: Create student profiles through the app
2. **Test Learning Sessions**: Use the AI teachers for different subjects
3. **Monitor Progress**: Check the adaptive learning recommendations
4. **Customize**: Modify AI teacher personalities and capabilities

## 📈 **Production Considerations**

For production deployment:

1. **Environment Variables**: Use proper environment variable management
2. **Security**: Implement proper authentication and authorization
3. **Monitoring**: Set up logging and analytics
4. **Scaling**: Configure Supabase for your expected load

## 🎯 **Features Ready to Use**

✅ **AI-Powered Teachers** - Gemini Flash 2.0 integration
✅ **Adaptive Learning** - Real-time personalization
✅ **Special Needs Support** - Comprehensive accessibility
✅ **Progress Tracking** - Detailed analytics
✅ **Emotional AI** - Mood detection and support
✅ **Multi-Agent System** - LangGraph + CrewAI architecture
✅ **Real-time Database** - Supabase integration
✅ **Parent Dashboard** - Progress insights

Your virtual school is ready to revolutionize special needs education! 🌟
