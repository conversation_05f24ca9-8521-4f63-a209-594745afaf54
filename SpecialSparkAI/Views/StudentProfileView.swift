import SwiftUI

struct StudentProfileView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @StateObject private var settingsManager = SettingsManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var selectedGradeLevel: GradeLevel = .grade1
    @State private var selectedSpecialNeeds: Set<SpecialNeedsType> = [.none]
    @State private var showingDemoProfiles = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if mockAuthService.isAuthenticated {
                    authenticatedProfileView
                } else {
                    authenticationView
                }
            }
            .padding()
            .navigationTitle("Student Profile")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var authenticatedProfileView: some View {
        VStack(spacing: 24) {
            // Current Student Info
            if let student = mockAuthService.currentStudent {
                VStack(spacing: 16) {
                    // Student Header
                    VStack(spacing: 8) {
                        Circle()
                            .fill(LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .frame(width: 80, height: 80)
                            .overlay(
                                Text(String(student.firstName.prefix(1)))
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )

                        Text(student.fullName)
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("\(student.gradeLevel.displayName) • \(student.schoolLevel.displayName)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    // Current Settings
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Current Settings")
                            .font(.headline)
                            .padding(.bottom, 4)

                        HStack {
                            Text("Grade Level:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(student.gradeLevel.displayName)
                                .foregroundColor(.blue)
                        }

                        HStack {
                            Text("School Level:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(student.schoolLevel.displayName)
                                .foregroundColor(.blue)
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Special Needs Support:")
                                .fontWeight(.medium)

                            if student.specialNeeds == [.none] || student.specialNeeds.isEmpty {
                                Text("No special needs")
                                    .foregroundColor(.secondary)
                            } else {
                                ForEach(student.specialNeeds, id: \.self) { need in
                                    if need != .none {
                                        HStack {
                                            Image(systemName: "heart.fill")
                                                .foregroundColor(.pink)
                                                .font(.caption)
                                            Text(need.displayName)
                                                .foregroundColor(.pink)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }

                // Update Profile Section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Update Profile")
                        .font(.headline)

                    // Grade Level Picker
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Grade Level")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Picker("Grade Level", selection: $selectedGradeLevel) {
                            ForEach(GradeLevel.allCases, id: \.self) { grade in
                                Text(grade.displayName).tag(grade)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }

                    // Special Needs Selection
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Special Needs Support")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 8) {
                            ForEach(SpecialNeedsType.allCases, id: \.self) { need in
                                Button(action: {
                                    if need == .none {
                                        selectedSpecialNeeds = [.none]
                                    } else {
                                        selectedSpecialNeeds.remove(.none)
                                        if selectedSpecialNeeds.contains(need) {
                                            selectedSpecialNeeds.remove(need)
                                            if selectedSpecialNeeds.isEmpty {
                                                selectedSpecialNeeds.insert(.none)
                                            }
                                        } else {
                                            selectedSpecialNeeds.insert(need)
                                        }
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: selectedSpecialNeeds.contains(need) ? "checkmark.circle.fill" : "circle")
                                            .foregroundColor(selectedSpecialNeeds.contains(need) ? .blue : .gray)

                                        Text(need.displayName)
                                            .font(.caption)
                                            .foregroundColor(.primary)

                                        Spacer()
                                    }
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 6)
                                    .background(selectedSpecialNeeds.contains(need) ? Color.blue.opacity(0.1) : Color(.systemGray6))
                                    .cornerRadius(6)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }

                    // Update Button
                    Button("Update Profile") {
                        mockAuthService.updateStudentProfile(
                            gradeLevel: selectedGradeLevel,
                            specialNeeds: Array(selectedSpecialNeeds)
                        )

                        // Update settings manager to trigger AI teacher refresh
                        settingsManager.updateGrade(selectedGradeLevel.displayName)
                    }
                    .buttonStyle(.borderedProminent)
                    .frame(maxWidth: .infinity)
                }

                Spacer()

                // Sign Out Button
                Button("Sign Out") {
                    mockAuthService.signOut()
                    dismiss()
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
            }
        }
        .onAppear {
            if let student = mockAuthService.currentStudent {
                selectedGradeLevel = student.gradeLevel
                selectedSpecialNeeds = Set(student.specialNeeds)
            }
        }
    }

    private var authenticationView: some View {
        VStack(spacing: 24) {
            // Welcome Header
            VStack(spacing: 12) {
                Image(systemName: "graduationcap.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)

                Text("Welcome to SpecialSpark AI")
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)

                Text("The world's first AI-native virtual school designed for every learner")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            // Demo Profiles
            VStack(alignment: .leading, spacing: 16) {
                Text("Try Demo Profiles")
                    .font(.headline)

                LazyVGrid(columns: [GridItem(.flexible())], spacing: 12) {
                    ForEach(mockAuthService.createDemoProfiles(), id: \.name) { profile in
                        Button(action: {
                            Task {
                                await mockAuthService.signInWithDemoProfile(profile)
                                dismiss()
                            }
                        }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(profile.name)
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.primary)

                                    Text(profile.description)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                Image(systemName: "arrow.right.circle.fill")
                                    .foregroundColor(.blue)
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }

            Spacer()
        }
    }
}
