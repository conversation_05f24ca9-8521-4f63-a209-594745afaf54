//
//  GradeSelectionView.swift
//  SpecialSparkAI
//
//  Grade Level Selection Interface
//

import SwiftUI

struct GradeSelectionView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @StateObject private var mockAuthService = MockAuthService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var selectedGradeLevel: GradeLevel = .grade3
    @State private var showingConfirmation = false
    @State private var isUpdating = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection

                // Current Grade Display
                currentGradeSection

                // School Level Sections
                ScrollView {
                    VStack(spacing: 24) {
                        ForEach(SchoolLevel.allCases, id: \.self) { schoolLevel in
                            schoolLevelSection(schoolLevel)
                        }
                    }
                    .padding()
                }

                // Update Button
                updateButton
            }
            .navigationTitle("Select Grade Level")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadCurrentGrade()
            }
            .alert("Update Grade Level?", isPresented: $showingConfirmation) {
                But<PERSON>("Cancel", role: .cancel) { }
                But<PERSON>("Update") {
                    updateGradeLevel()
                }
            } message: {
                Text("Changing your grade level will update your AI teachers and learning content. This action will also update your student profile.")
            }
        }
    }

    // MARK: - UI Components

    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "graduationcap.circle.fill")
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )

            VStack(spacing: 4) {
                Text("Choose Your Grade Level")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("This will personalize your AI teachers and learning content")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }

    private var currentGradeSection: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Current Grade")
                    .font(.headline)
                    .foregroundColor(.secondary)

                Spacer()

                Text(settingsManager.currentGrade)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }

            if let student = mockAuthService.currentStudent {
                HStack {
                    Text("School Level")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text(student.schoolLevel.displayName)
                        .font(.subheadline)
                        .foregroundColor(.purple)
                }
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .padding(.horizontal)
    }

    private func schoolLevelSection(_ schoolLevel: SchoolLevel) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: schoolLevelIcon(schoolLevel))
                    .foregroundColor(schoolLevelColor(schoolLevel))

                Text(schoolLevel.displayName)
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(schoolLevel.grades, id: \.self) { grade in
                    GradeSelectionCard(
                        grade: grade,
                        isSelected: selectedGradeLevel == grade,
                        isCurrent: currentGradeLevel == grade
                    ) {
                        selectedGradeLevel = grade
                    }
                }
            }
        }
    }

    private var updateButton: some View {
        VStack(spacing: 12) {
            if selectedGradeLevel != currentGradeLevel {
                Button(action: {
                    showingConfirmation = true
                }) {
                    HStack {
                        if isUpdating {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "arrow.up.circle.fill")
                        }

                        Text(isUpdating ? "Updating..." : "Update Grade Level")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(isUpdating)
                .padding(.horizontal)
            }
        }
        .padding(.bottom)
        .background(.ultraThinMaterial)
    }

    // MARK: - Helper Methods

    private var currentGradeLevel: GradeLevel {
        if let student = mockAuthService.currentStudent {
            return student.gradeLevel
        }
        // Parse from settings manager string
        return GradeLevel.allCases.first { grade in
            grade.displayName == settingsManager.currentGrade
        } ?? .grade3
    }

    private func loadCurrentGrade() {
        selectedGradeLevel = currentGradeLevel
    }

    private func updateGradeLevel() {
        isUpdating = true

        // Update settings manager
        settingsManager.updateGrade(selectedGradeLevel.displayName)

        // Update current student if available
        if let student = mockAuthService.currentStudent {
            student.gradeLevel = selectedGradeLevel
            student.schoolLevel = selectedGradeLevel.schoolLevel
        }

        // Simulate update delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isUpdating = false
            dismiss()
        }
    }

    private func schoolLevelIcon(_ schoolLevel: SchoolLevel) -> String {
        switch schoolLevel {
        case .elementary:
            return "building.fill"
        case .middle:
            return "building.2.fill"
        case .high:
            return "graduationcap.fill"
        }
    }

    private func schoolLevelColor(_ schoolLevel: SchoolLevel) -> Color {
        switch schoolLevel {
        case .elementary:
            return .green
        case .middle:
            return .orange
        case .high:
            return .purple
        }
    }
}

// MARK: - Grade Selection Card

struct GradeSelectionCard: View {
    let grade: GradeLevel
    let isSelected: Bool
    let isCurrent: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(grade.rawValue)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(textColor)

                Text(grade.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(textColor)
                    .multilineTextAlignment(.center)

                if isCurrent {
                    Text("Current")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(.blue)
                        .cornerRadius(4)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(backgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var backgroundColor: Color {
        if isCurrent {
            return .blue.opacity(0.1)
        } else if isSelected {
            return .blue.opacity(0.05)
        } else {
            return .clear
        }
    }

    private var borderColor: Color {
        if isCurrent {
            return .blue
        } else if isSelected {
            return .blue.opacity(0.5)
        } else {
            return .gray.opacity(0.3)
        }
    }

    private var borderWidth: CGFloat {
        if isCurrent || isSelected {
            return 2
        } else {
            return 1
        }
    }

    private var textColor: Color {
        if isCurrent || isSelected {
            return .blue
        } else {
            return .primary
        }
    }
}

#Preview {
    GradeSelectionView()
}
