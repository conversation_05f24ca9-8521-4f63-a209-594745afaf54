//
//  ActiveAssessmentView.swift
//  SpecialSparkAI
//
//  Phase 4: Active Assessment Interface
//

import SwiftUI

struct ActiveAssessmentView: View {
    let assessment: Assessment
    let session: AssessmentSession

    @StateObject private var assessmentEngine = AssessmentEngineService.shared
    @State private var currentQuestionIndex = 0
    @State private var selectedAnswer = ""
    @State private var showingHint = false
    @State private var startTime = Date()
    @State private var showingConfirmation = false

    private var currentQuestion: AssessmentQuestion? {
        guard currentQuestionIndex < assessment.questions.count else { return nil }
        return assessment.questions[currentQuestionIndex]
    }

    private var progress: Double {
        let completed = Double(session.responses.count)
        let total = Double(assessment.questions.count)
        return total > 0 ? completed / total : 0.0
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header with progress
            headerView

            // Question content
            if let question = currentQuestion {
                questionView(question)
            } else {
                completionView
            }
        }
        .navigationTitle("Assessment")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Finish") {
                    showingConfirmation = true
                }
            }
        }
        .alert("Finish Assessment?", isPresented: $showingConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Finish", role: .destructive) {
                Task {
                    await assessmentEngine.completeAssessment()
                }
            }
        } message: {
            Text("Are you sure you want to finish this assessment? You won't be able to change your answers.")
        }
    }

    // MARK: - Header View

    private var headerView: some View {
        VStack(spacing: 12) {
            // Progress bar
            VStack(spacing: 8) {
                HStack {
                    Text("Question \(currentQuestionIndex + 1) of \(assessment.questions.count)")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Spacer()

                    Text(timeElapsed)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                }

                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            }

            // Assessment info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(assessment.title)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(assessment.subject)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Score")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("\(currentScore)/\(session.responses.count)")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    // MARK: - Question View

    private func questionView(_ question: AssessmentQuestion) -> some View {
        ScrollView {
            VStack(spacing: 24) {
                // Question text
                VStack(alignment: .leading, spacing: 12) {
                    Text("Question \(currentQuestionIndex + 1)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)

                    Text(question.questionText)
                        .font(.title3)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.blue.opacity(0.05))
                )

                // Answer options
                answerOptionsView(question)

                // Hints section
                if !question.hints.isEmpty {
                    hintsSection(question)
                }

                // Navigation buttons
                navigationButtons
            }
            .padding()
        }
    }

    // MARK: - Answer Options View

    private func answerOptionsView(_ question: AssessmentQuestion) -> some View {
        VStack(spacing: 12) {
            switch question.questionType {
            case .multipleChoice:
                multipleChoiceOptions(question)
            case .trueFalse:
                trueFalseOptions()
            case .shortAnswer:
                shortAnswerField()
            case .essay:
                essayField()
            case .fillInBlank:
                fillInBlankField()
            case .matching:
                matchingOptions(question)
            case .ordering:
                orderingOptions(question)
            case .dragDrop:
                dragAndDropOptions(question)
            case .hotspot:
                hotspotOptions(question)
            case .drawing:
                drawingOptions(question)
            case .recording:
                recordingOptions(question)
            case .simulation:
                simulationOptions(question)
            }
        }
    }

    private func multipleChoiceOptions(_ question: AssessmentQuestion) -> some View {
        VStack(spacing: 12) {
            ForEach(question.options ?? [], id: \.id) { option in
                Button(action: { selectedAnswer = option.text }) {
                    HStack {
                        Text(option.text)
                            .font(.subheadline)
                            .multilineTextAlignment(.leading)

                        Spacer()

                        Image(systemName: selectedAnswer == option.text ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(selectedAnswer == option.text ? .blue : .secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedAnswer == option.text ? .blue.opacity(0.1) : .gray.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(selectedAnswer == option.text ? .blue : .clear, lineWidth: 2)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    private func trueFalseOptions() -> some View {
        HStack(spacing: 16) {
            Button(action: { selectedAnswer = "True" }) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("True")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(selectedAnswer == "True" ? .green.opacity(0.1) : .gray.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(selectedAnswer == "True" ? .green : .clear, lineWidth: 2)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: { selectedAnswer = "False" }) {
                HStack {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                    Text("False")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(selectedAnswer == "False" ? .red.opacity(0.1) : .gray.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(selectedAnswer == "False" ? .red : .clear, lineWidth: 2)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    private func shortAnswerField() -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Your Answer:")
                .font(.subheadline)
                .fontWeight(.medium)

            TextField("Type your answer here...", text: $selectedAnswer)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .font(.subheadline)
        }
    }

    private func essayField() -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Your Essay:")
                .font(.subheadline)
                .fontWeight(.medium)

            TextEditor(text: $selectedAnswer)
                .frame(minHeight: 120)
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(.gray.opacity(0.3), lineWidth: 1)
                )
        }
    }

    private func fillInBlankField() -> some View {
        shortAnswerField()
    }

    private func matchingOptions(_ question: AssessmentQuestion) -> some View {
        Text("Matching questions coming soon...")
            .foregroundColor(.secondary)
    }

    private func orderingOptions(_ question: AssessmentQuestion) -> some View {
        Text("Ordering questions coming soon...")
            .foregroundColor(.secondary)
    }

    private func dragAndDropOptions(_ question: AssessmentQuestion) -> some View {
        Text("Drag and drop questions coming soon...")
            .foregroundColor(.secondary)
    }

    private func hotspotOptions(_ question: AssessmentQuestion) -> some View {
        Text("Hotspot questions coming soon...")
            .foregroundColor(.secondary)
    }

    private func drawingOptions(_ question: AssessmentQuestion) -> some View {
        Text("Drawing questions coming soon...")
            .foregroundColor(.secondary)
    }

    private func recordingOptions(_ question: AssessmentQuestion) -> some View {
        Text("Recording questions coming soon...")
            .foregroundColor(.secondary)
    }

    private func simulationOptions(_ question: AssessmentQuestion) -> some View {
        Text("Simulation questions coming soon...")
            .foregroundColor(.secondary)
    }

    // MARK: - Hints Section

    private func hintsSection(_ question: AssessmentQuestion) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Button(action: { showingHint.toggle() }) {
                HStack {
                    Image(systemName: "lightbulb")
                        .foregroundColor(.orange)

                    Text("Need a hint?")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Spacer()

                    Image(systemName: showingHint ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.orange.opacity(0.1))
                )
            }
            .buttonStyle(PlainButtonStyle())

            if showingHint && !question.hints.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(question.hints, id: \.id) { hint in
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "lightbulb.fill")
                                .foregroundColor(.orange)
                                .font(.caption)

                            Text(hint.text)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.orange.opacity(0.05))
                        )
                    }
                }
            }
        }
    }

    // MARK: - Navigation Buttons

    private var navigationButtons: some View {
        HStack(spacing: 16) {
            if currentQuestionIndex > 0 {
                Button("Previous") {
                    currentQuestionIndex -= 1
                    selectedAnswer = ""
                }
                .buttonStyle(.bordered)
            }

            Spacer()

            Button(currentQuestionIndex < assessment.questions.count - 1 ? "Next" : "Finish") {
                submitAnswer()
            }
            .buttonStyle(.borderedProminent)
            .disabled(selectedAnswer.isEmpty)
        }
    }

    // MARK: - Completion View

    private var completionView: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 64))
                .foregroundColor(.green)

            VStack(spacing: 8) {
                Text("Assessment Complete!")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Great job! Your responses have been submitted.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("View Results") {
                Task {
                    await assessmentEngine.completeAssessment()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding(40)
    }

    // MARK: - Helper Methods

    private var timeElapsed: String {
        let elapsed = Date().timeIntervalSince(startTime)
        let minutes = Int(elapsed) / 60
        let seconds = Int(elapsed) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private var currentScore: Int {
        return session.responses.filter { $0.isCorrect ?? false }.count
    }

    private func submitAnswer() {
        guard let question = currentQuestion, !selectedAnswer.isEmpty else { return }

        let responseTime = Date().timeIntervalSince(startTime)

        Task {
            await assessmentEngine.submitResponse(
                questionId: question.id,
                response: selectedAnswer,
                responseTime: responseTime
            )

            if currentQuestionIndex < assessment.questions.count - 1 {
                currentQuestionIndex += 1
                selectedAnswer = ""
                startTime = Date()
            }
        }
    }
}

#Preview {
    ActiveAssessmentView(
        assessment: Assessment(
            id: UUID(),
            title: "Sample Assessment",
            description: "A sample assessment",
            type: .formative,
            subject: "Math",
            gradeLevel: "Grade 3",
            difficulty: .beginner,
            estimatedDuration: 15,
            questions: [],
            rubrics: [],
            adaptiveSettings: AdaptiveAssessmentSettings(
                isAdaptive: false,
                adaptationAlgorithm: .irt,
                minimumQuestions: 5,
                maximumQuestions: 10,
                terminationCriteria: [],
                difficultyAdjustment: DifficultyAdjustment(
                    initialDifficulty: 0.5,
                    adjustmentRate: 0.1,
                    minimumDifficulty: 0.1,
                    maximumDifficulty: 0.9,
                    smoothingFactor: 0.3
                ),
                contentBalancing: ContentBalancing(
                    enforceBalance: false,
                    contentAreas: [],
                    balancingStrategy: .equal
                )
            ),
            accommodations: [],
            scoringCriteria: ScoringCriteria(
                totalPoints: 10,
                passingScore: 0.7,
                gradingScale: GradingScale(
                    type: .percentage,
                    ranges: [
                        GradeRange(id: UUID(), grade: "A", minimumScore: 90, maximumScore: 100, description: "Excellent"),
                        GradeRange(id: UUID(), grade: "B", minimumScore: 80, maximumScore: 89, description: "Good"),
                        GradeRange(id: UUID(), grade: "C", minimumScore: 70, maximumScore: 79, description: "Satisfactory"),
                        GradeRange(id: UUID(), grade: "F", minimumScore: 0, maximumScore: 69, description: "Needs Improvement")
                    ]
                ),
                weightedScoring: false,
                partialCreditRules: [],
                bonusPoints: []
            ),
            isActive: true,
            createdAt: "",
            updatedAt: ""
        ),
        session: AssessmentSession(
            id: UUID(),
            assessmentId: UUID(),
            studentId: UUID(),
            startTime: "",
            endTime: nil,
            status: .inProgress,
            responses: [],
            score: nil,
            adaptiveData: AdaptiveSessionData(
                abilityEstimate: 0.5,
                standardError: 0.3,
                questionsAdministered: 0,
                adaptationHistory: [],
                terminationReason: nil
            ),
            accommodationsUsed: [],
            technicalIssues: [],
            proctorNotes: []
        )
    )
}
