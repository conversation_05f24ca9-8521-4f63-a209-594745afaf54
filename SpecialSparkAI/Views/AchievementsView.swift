//
//  AchievementsView.swift
//  SpecialSparkAI
//
//  Phase 3: Gamification - Achievements and Progress Display
//

import SwiftUI

struct AchievementsView: View {
    @StateObject private var gamificationEngine = GamificationEngine.shared
    @StateObject private var mockAuthService = MockAuthService.shared
    @State private var selectedCategory: GamificationAchievementCategory = .learning
    @State private var showingAchievementDetail = false
    @State private var selectedAchievement: Achievement?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Header
                progressHeader

                // Category Filter
                categoryFilter

                // Achievements Grid
                achievementsGrid
            }
            .navigationTitle("Achievements")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadProgress()
            }
        }
        .sheet(isPresented: $showingAchievementDetail) {
            if let achievement = selectedAchievement {
                AchievementDetailView(achievement: achievement)
            }
        }
    }

    // MARK: - Progress Header
    private var progressHeader: some View {
        VStack(spacing: 16) {
            if let progress = gamificationEngine.studentProgress {
                // Level and XP
                HStack(spacing: 20) {
                    VStack(spacing: 4) {
                        Text("Level")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(progress.currentLevel)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Progress to Level \(progress.currentLevel + 1)")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            Text("\(progress.experiencePoints)/\(progress.experiencePoints + progress.pointsToNextLevel)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        ProgressView(value: Double(progress.experiencePoints), total: Double(progress.experiencePoints + progress.pointsToNextLevel))
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    }

                    VStack(spacing: 4) {
                        Text("Points")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(progress.totalPoints)")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                    }
                }

                // Streak and Stats
                HStack(spacing: 30) {
                    StatCard(title: "Streak", value: "\(progress.streakDays)", icon: "flame.fill", color: .red)
                    StatCard(title: "Badges", value: "\(progress.badges.count)", icon: "rosette", color: .purple)
                    StatCard(title: "Accuracy", value: "\(Int(progress.statistics.accuracy))%", icon: "target", color: .green)
                }
            } else {
                ProgressView("Loading progress...")
                    .frame(height: 100)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    // MARK: - Category Filter
    private var categoryFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(GamificationAchievementCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        category: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
    }

    // MARK: - Achievements Grid
    private var achievementsGrid: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(filteredAchievements) { achievement in
                    GamificationAchievementCard(
                        achievement: achievement,
                        isUnlocked: isUnlocked(achievement)
                    ) {
                        selectedAchievement = achievement
                        showingAchievementDetail = true
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Computed Properties
    private var filteredAchievements: [Achievement] {
        return gamificationEngine.availableAchievements.filter { $0.category == selectedCategory }
    }

    private func isUnlocked(_ achievement: Achievement) -> Bool {
        return gamificationEngine.studentProgress?.achievementsUnlocked.contains(achievement.id) ?? false
    }

    // MARK: - Actions
    private func loadProgress() {
        guard let student = mockAuthService.currentStudent else { return }

        Task {
            await gamificationEngine.loadProgress(for: student)
        }
    }
}

// StatCard is defined in UIComponents.swift

// MARK: - Category Button
struct CategoryButton: View {
    let category: GamificationAchievementCategory
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(category.rawValue)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? Color.blue : Color(.systemGray6))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Achievement Card (Custom for GamificationEngine)
struct GamificationAchievementCard: View {
    let achievement: Achievement
    let isUnlocked: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Badge Icon
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: isUnlocked ?
                                    [Color(hex: achievement.badgeColor), Color(hex: achievement.badgeColor).opacity(0.7)] :
                                    [Color.gray.opacity(0.3), Color.gray.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)

                    Image(systemName: achievement.iconName)
                        .font(.title2)
                        .foregroundColor(isUnlocked ? .white : .gray)

                    if !isUnlocked {
                        Image(systemName: "lock.fill")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .offset(x: 20, y: 20)
                    }
                }

                VStack(spacing: 4) {
                    Text(achievement.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .foregroundColor(isUnlocked ? .primary : .secondary)

                    Text(achievement.achievementDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)

                        Text("\(achievement.points) pts")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                    }
                }
            }
            .padding()
            .frame(maxWidth: .infinity, minHeight: 160)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isUnlocked ? Color(hex: achievement.badgeColor) : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isUnlocked ? 1.0 : 0.95)
        .opacity(isUnlocked ? 1.0 : 0.7)
    }
}

// MARK: - Achievement Detail View
struct AchievementDetailView: View {
    let achievement: Achievement
    @Environment(\.dismiss) private var dismiss
    @StateObject private var gamificationEngine = GamificationEngine.shared

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Large Badge
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color(hex: achievement.badgeColor), Color(hex: achievement.badgeColor).opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)

                    Image(systemName: achievement.iconName)
                        .font(.system(size: 50))
                        .foregroundColor(.white)
                }

                VStack(spacing: 8) {
                    Text(achievement.title)
                        .font(.title)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)

                    Text(achievement.achievementDescription)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // Requirements
                VStack(alignment: .leading, spacing: 12) {
                    Text("Requirements")
                        .font(.headline)
                        .fontWeight(.semibold)

                    ForEach(achievement.requirements, id: \.id) { requirement in
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)

                            Text(requirement.description)
                                .font(.subheadline)

                            Spacer()
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                )

                // Points and Difficulty
                HStack(spacing: 20) {
                    VStack {
                        Text("Points")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(achievement.points)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                    }

                    VStack {
                        Text("Difficulty")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(achievement.difficulty.rawValue)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color(hex: achievement.difficulty.color))
                    }
                }

                Spacer()
            }
            .padding()
            .navigationTitle("Achievement")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// Color extension is defined in ConversationView.swift

#Preview {
    AchievementsView()
}
