//
//  SensorySettingsView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct SensorySettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @State private var showingPreview = false

    var body: some View {
        NavigationView {
            List {
                Section("Visual Settings") {
                    Toggle("Reduce Motion", isOn: $settingsManager.reduceMotion)
                    Toggle("High Contrast", isOn: $settingsManager.highContrastMode)
                    Toggle("Large Text", isOn: $settingsManager.largeText)
                }

                Section("Audio Settings") {
                    Toggle("Mute All Sounds", isOn: $settingsManager.muteAllSounds)
                    Toggle("Audio Descriptions", isOn: $settingsManager.enableAudioDescriptions)
                }

                Section("Motion & Animation") {
                    Toggle("Disable Auto-Play", isOn: $settingsManager.disableAutoPlay)
                    Toggle("Slower Transitions", isOn: $settingsManager.slowerTransitions)
                }

                Section("Preview") {
                    Button("Preview Settings") {
                        showingPreview = true
                    }
                }
            }
            .navigationTitle("Sensory Settings")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingPreview) {
            SensoryPreviewView()
        }
    }






}

// MARK: - Preview View
struct SensoryPreviewView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var settingsManager = SettingsManager.shared

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Sample content with current settings applied
                    Text("Preview of Your Settings")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("This is how text will appear with your current settings.")
                        .font(.body)
                        .multilineTextAlignment(.center)

                    // Sample buttons
                    HStack(spacing: 15) {
                        Button("Sample Button") {
                            // Sample action
                        }
                        .buttonStyle(.borderedProminent)

                        Button("Another Button") {
                            // Sample action
                        }
                        .buttonStyle(.bordered)
                    }

                    // Sample animation placeholder
                    Circle()
                        .fill(.blue)
                        .frame(width: 50, height: 50)

                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Settings Preview")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
