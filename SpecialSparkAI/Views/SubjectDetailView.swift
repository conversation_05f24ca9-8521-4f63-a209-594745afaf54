//
//  SubjectDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct SubjectDetailView: View {
    let subject: Subject
    let gradeLevel: GradeLevel
    @StateObject private var lessonDataService = LessonDataService()
    @StateObject private var subjectDataService = SubjectDataService()
    @State private var selectedLesson: Lesson?
    @State private var showingLessonDetail = false
    @Environment(\.dismiss) private var dismiss
    
    private var filteredSubjects: [Subject] {
        return subjectDataService.getSubjectsForGrade(gradeLevel)
    }
    
    private var lessonsForSubject: [Lesson] {
        return lessonDataService.getLessonsForSubject(subject.id, gradeLevel: gradeLevel)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    subjectHeaderView
                    
                    // All Subjects for Grade
                    allSubjectsView
                    
                    // Lessons for Selected Subject
                    if !lessonsForSubject.isEmpty {
                        lessonsView
                    }
                }
                .padding()
            }
            .navigationTitle("Academic Center")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingLessonDetail) {
            if let lesson = selectedLesson {
                LessonDetailView(lesson: lesson)
            }
        }
    }
    
    private var subjectHeaderView: some View {
        VStack(spacing: 15) {
            Text("Grade \(gradeLevel.displayName) Subjects")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Choose a subject to explore lessons and activities")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var allSubjectsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Available Subjects")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                ForEach(filteredSubjects) { subject in
                    SubjectCard(
                        subject: subject,
                        gradeLevel: gradeLevel,
                        lessonCount: lessonDataService.getLessonsForSubject(subject.id, gradeLevel: gradeLevel).count
                    ) {
                        // Navigate to subject lessons
                        self.selectedLesson = lessonsForSubject.first
                        showingLessonDetail = true
                    }
                }
            }
        }
    }
    
    private var lessonsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Lessons in \(subject.name)")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVStack(spacing: 12) {
                ForEach(lessonsForSubject) { lesson in
                    LessonCard(lesson: lesson) {
                        selectedLesson = lesson
                        showingLessonDetail = true
                    }
                }
            }
        }
    }
}

struct SubjectCard: View {
    let subject: Subject
    let gradeLevel: GradeLevel
    let lessonCount: Int
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                // Subject icon
                Image(systemName: subject.icon)
                    .font(.largeTitle)
                    .foregroundColor(subject.color)
                
                // Content
                VStack(spacing: 8) {
                    Text(subject.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Text(subject.subjectDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text("\(lessonCount) lessons")
                        .font(.caption2)
                        .foregroundColor(subject.color)
                }
                
                // Action button
                Text("Start Learning")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 6)
                    .background(subject.color)
                    .cornerRadius(12)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct LessonCard: View {
    let lesson: Lesson
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                // Lesson icon
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: "book.fill")
                            .foregroundColor(.white)
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 5) {
                    Text(lesson.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(lesson.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack {
                        Label("\(lesson.duration) min", systemImage: "clock")
                        Spacer()
                        Label(lesson.difficulty.displayName, systemImage: "chart.bar")
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Progress indicator
                VStack {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    SubjectDetailView(
        subject: Subject(
            name: "Mathematics",
            subjectDescription: "Numbers, calculations, and problem solving",
            icon: "function",
            color: .blue,
            category: .core,
            gradeLevels: [.grade3],
            isActive: true
        ),
        gradeLevel: .grade3
    )
}
