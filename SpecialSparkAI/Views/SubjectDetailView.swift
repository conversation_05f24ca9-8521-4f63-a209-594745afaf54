//
//  SubjectDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct SubjectDetailView: View {
    let subject: Subject
    let gradeLevel: GradeLevel
    @StateObject private var lessonDataService = LessonDataService()
    @StateObject private var subjectDataService = SubjectDataService()
    @State private var selectedLesson: Lesson?
    @State private var showingLessonDetail = false
    @Environment(\.dismiss) private var dismiss

    private var filteredSubjects: [Subject] {
        return subjectDataService.getSubjectsForGrade(gradeLevel)
    }

    private var lessonsForSubject: [Lesson] {
        return lessonDataService.getLessonsForSubject(subject.id, gradeLevel: gradeLevel)
    }

    var body: some View {
        NavigationView {
            ScrollView(.vertical, showsIndicators: true) {
                VStack(spacing: 20) {
                    // Header
                    subjectHeaderView

                    // All Subjects for Grade
                    allSubjectsView

                    // Lessons for Selected Subject
                    if !lessonsForSubject.isEmpty {
                        lessonsView
                    }
                }
                .padding()
            }
            .navigationTitle("Academic Center")
            .navigationBarTitleDisplayMode(.large)

        }
        .sheet(isPresented: $showingLessonDetail) {
            if let lesson = selectedLesson {
                LessonDetailView(lesson: lesson, student: nil)
            }
        }
    }

    private var subjectHeaderView: some View {
        VStack(spacing: 15) {
            Text("Grade \(gradeLevel.displayName) Subjects")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Choose a subject to explore lessons and activities")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    private var allSubjectsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Available Subjects")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                ForEach(filteredSubjects) { subject in
                    SubjectCard(
                        subject: subject,
                        gradeLevel: gradeLevel,
                        lessonCount: lessonDataService.getLessonsForSubject(subject.id, gradeLevel: gradeLevel).count
                    ) {
                        // Navigate to subject lessons
                        self.selectedLesson = lessonsForSubject.first
                        showingLessonDetail = true
                    }
                }
            }
        }
    }

    private var lessonsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Lessons in \(subject.name)")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVStack(spacing: 12) {
                ForEach(lessonsForSubject) { lesson in
                    LessonCard(lesson: lesson, student: nil) {
                        selectedLesson = lesson
                        showingLessonDetail = true
                    }
                }
            }
        }
    }
}

struct SubjectCard: View {
    let subject: Subject
    let gradeLevel: GradeLevel
    let lessonCount: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                // Subject icon
                Image(systemName: subject.icon)
                    .font(.largeTitle)
                    .foregroundColor(subject.color)

                // Content
                VStack(spacing: 8) {
                    Text(subject.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)

                    Text(subject.subjectDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    Text("\(lessonCount) lessons")
                        .font(.caption2)
                        .foregroundColor(subject.color)
                }

                // Action button
                Text("Start Learning")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 6)
                    .background(subject.color)
                    .cornerRadius(12)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



#Preview {
    SubjectDetailView(
        subject: Subject(
            name: "Mathematics",
            code: "MATH",
            category: .core,
            schoolLevels: [.elementary],
            gradeLevels: [.grade3],
            description: "Numbers, calculations, and problem solving"
        ),
        gradeLevel: .grade3
    )
}
