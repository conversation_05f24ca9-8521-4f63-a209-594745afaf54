//
//  ConversationView.swift
//  SpecialSparkAI
//
//  AI Teacher Conversation Interface
//

import SwiftUI

struct ConversationView: View {
    let student: Student
    let teacher: AITeacherPersonality

    @StateObject private var conversationManager = ConversationManager.shared
    @State private var messageText = ""
    @State private var isLoading = false
    @State private var conversation: Conversation?
    @State private var showingError = false
    @State private var errorMessage = ""

    var body: some View {
        VStack(spacing: 0) {
            // Teacher Header
            teacherHeader

            // Messages List
            messagesScrollView

            // Message Input
            messageInputView
        }
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            startConversation()
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }

    // MARK: - Teacher Header
    private var teacherHeader: some View {
        HStack(spacing: 12) {
            // Teacher Avatar
            Circle()
                .fill(
                    LinearGradient(
                        colors: [Color(hex: teacher.backgroundColor), Color(hex: teacher.backgroundColor).opacity(0.7)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 50, height: 50)
                .overlay(
                    Image(systemName: teacher.avatarImageName)
                        .font(.title2)
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(teacher.name)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text("\(teacher.subject) Teacher")
                    .font(.caption)
                    .foregroundColor(.secondary)

                if isLoading {
                    HStack(spacing: 4) {
                        Image(systemName: "ellipsis")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("typing...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Spacer()

            // Status Indicator
            Circle()
                .fill(Color.green)
                .frame(width: 8, height: 8)
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(radius: 1)
    }

    // MARK: - Messages Scroll View
    private var messagesScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 12) {
                    if let conversation = conversation {
                        ForEach(conversation.messages) { message in
                            MessageBubble(
                                message: message,
                                teacher: teacher,
                                student: student
                            )
                            .id(message.id)
                        }
                    }

                    // Loading indicator
                    if isLoading {
                        HStack {
                            TypingIndicator()
                            Spacer()
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
            }
            .onChange(of: conversation?.messages.count) { oldValue, newValue in
                // Auto-scroll to bottom when new message arrives
                if let lastMessage = conversation?.messages.last {
                    withAnimation(.easeOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    // MARK: - Message Input
    private var messageInputView: some View {
        VStack(spacing: 8) {
            // Quick Response Buttons
            if messageText.isEmpty && !isLoading {
                quickResponseButtons
            }

            // Text Input
            HStack(spacing: 12) {
                TextField("Type your message...", text: $messageText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)
                    .disabled(isLoading)

                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty ? .gray : Color(hex: teacher.backgroundColor))
                }
                .disabled(messageText.isEmpty || isLoading)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(radius: 1)
    }

    // MARK: - Quick Response Buttons
    private var quickResponseButtons: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(getQuickResponses(), id: \.self) { response in
                    Button(response) {
                        messageText = response
                        sendMessage()
                    }
                    .buttonStyle(QuickResponseButtonStyle())
                }
            }
            .padding(.horizontal)
        }
    }

    // MARK: - Actions
    private func startConversation() {
        Task {
            let newConversation = await conversationManager.startConversation(
                student: student,
                teacher: teacher
            )

            await MainActor.run {
                self.conversation = newConversation
            }
        }
    }

    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              let conversation = conversation else { return }

        let message = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        messageText = ""

        Task {
            await MainActor.run {
                self.isLoading = true
            }

            do {
                _ = try await conversationManager.sendMessage(
                    message,
                    to: conversation,
                    student: student,
                    teacher: teacher
                )

                await MainActor.run {
                    self.isLoading = false
                }

            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = error.localizedDescription
                    self.showingError = true
                }
            }
        }
    }

    private func getQuickResponses() -> [String] {
        switch teacher.subject.lowercased() {
        case "mathematics", "math":
            return ["I need help", "Can you explain?", "Show me an example", "I don't understand"]
        case "science":
            return ["That's interesting!", "How does it work?", "Can we experiment?", "Tell me more"]
        case "reading", "english":
            return ["I love stories!", "What happens next?", "Can you help me read?", "I have a question"]
        default:
            return ["I need help", "Can you explain?", "That's cool!", "I have a question"]
        }
    }
}

// MARK: - Message Bubble
struct MessageBubble: View {
    let message: ConversationMessage
    let teacher: AITeacherPersonality
    let student: Student

    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer(minLength: 50)
                userMessageBubble
            } else {
                teacherMessageBubble
                Spacer(minLength: 50)
            }
        }
    }

    private var userMessageBubble: some View {
        VStack(alignment: .trailing, spacing: 4) {
            Text(message.content)
                .padding(12)
                .background(Color.blue)
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: 16))

            Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }

    private var teacherMessageBubble: some View {
        HStack(alignment: .top, spacing: 8) {
            // Teacher Avatar
            Circle()
                .fill(Color(hex: teacher.backgroundColor))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: teacher.avatarImageName)
                        .font(.caption)
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(message.content)
                    .padding(12)
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 16))

                Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Typing Indicator
struct TypingIndicator: View {
    @State private var animationPhase = 0

    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color.gray)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animationPhase == index ? 1.2 : 0.8)
                    .animation(
                        Animation.easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                        value: animationPhase
                    )
            }
        }
        .padding(12)
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .onAppear {
            animationPhase = 0
            Timer.scheduledTimer(withTimeInterval: 0.6, repeats: true) { _ in
                animationPhase = (animationPhase + 1) % 3
            }
        }
    }
}

// MARK: - Quick Response Button Style
struct QuickResponseButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color(.systemGray6))
            .foregroundColor(.primary)
            .clipShape(Capsule())
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Color Extension
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview {
    NavigationView {
        ConversationView(
            student: Student(
                firstName: "Alex",
                lastName: "Johnson",
                gradeLevel: .grade3
            ),
            teacher: AITeacherPersonality.createMathTeacher()
        )
    }
}
