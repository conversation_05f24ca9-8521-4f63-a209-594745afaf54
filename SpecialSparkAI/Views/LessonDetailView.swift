//
//  LessonDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct LessonDetailView: View {
    let lesson: Lesson
    @State private var selectedTab: LessonTab = .examples
    @State private var showingAIChat = false
    @Environment(\.dismiss) private var dismiss

    enum LessonTab: String, CaseIterable {
        case examples = "Examples"
        case quizzes = "Quizzes"
        case exercises = "Exercises"

        var icon: String {
            switch self {
            case .examples: return "lightbulb.fill"
            case .quizzes: return "questionmark.circle.fill"
            case .exercises: return "pencil.circle.fill"
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Lesson Header
                lessonHeaderView

                // Tab Selection
                tabSelectionView

                // Content Area
                TabView(selection: $selectedTab) {
                    examplesView
                        .tag(LessonTab.examples)

                    quizzesView
                        .tag(LessonTab.quizzes)

                    exercisesView
                        .tag(LessonTab.exercises)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(lesson.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingAIChat = true
                    }) {
                        Image(systemName: "message.circle.fill")
                            .foregroundColor(.blue)
                    }
                }
            }
        }
        .sheet(isPresented: $showingAIChat) {
            AITeacherChatView(subject: getSubjectForLesson())
        }
    }

    private var lessonHeaderView: some View {
        VStack(spacing: 15) {
            // Lesson info
            VStack(spacing: 8) {
                Text(lesson.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                HStack(spacing: 20) {
                    Label("\(lesson.duration) min", systemImage: "clock")
                    Label(lesson.difficulty.displayName, systemImage: "chart.bar")
                    Label("Grade \(lesson.gradeLevel.displayName)", systemImage: "graduationcap")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }

            // Learning objectives
            if !lesson.learningObjectives.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Learning Objectives")
                        .font(.headline)
                        .fontWeight(.semibold)

                    ForEach(lesson.learningObjectives, id: \.self) { objective in
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)

                            Text(objective)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                )
            }
        }
        .padding()
    }

    private var tabSelectionView: some View {
        HStack(spacing: 0) {
            ForEach(LessonTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.title3)

                        Text(tab.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .blue : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
            }
        }
        .background(Color(.systemGray6))
        .overlay(
            Rectangle()
                .fill(.blue)
                .frame(height: 2)
                .offset(x: tabOffset, y: 0)
                .animation(.easeInOut(duration: 0.3), value: selectedTab),
            alignment: .bottom
        )
    }

    private var tabOffset: CGFloat {
        let tabWidth = UIScreen.main.bounds.width / CGFloat(LessonTab.allCases.count)
        let index = LessonTab.allCases.firstIndex(of: selectedTab) ?? 0
        return (CGFloat(index) * tabWidth) - (UIScreen.main.bounds.width / 2) + (tabWidth / 2)
    }

    private var examplesView: some View {
        ScrollView {
            VStack(spacing: 20) {
                if lesson.topics.isEmpty {
                    // Sample examples for demonstration
                    sampleExamplesView
                } else {
                    ForEach(lesson.topics) { topic in
                        TopicExamplesView(topic: topic)
                    }
                }
            }
            .padding()
        }
    }

    private var quizzesView: some View {
        ScrollView {
            VStack(spacing: 20) {
                if lesson.topics.isEmpty {
                    sampleQuizzesView
                } else {
                    ForEach(lesson.topics) { topic in
                        TopicQuizzesView(topic: topic)
                    }
                }
            }
            .padding()
        }
    }

    private var exercisesView: some View {
        ScrollView {
            VStack(spacing: 20) {
                if lesson.topics.isEmpty {
                    sampleExercisesView
                } else {
                    ForEach(lesson.topics) { topic in
                        TopicExercisesView(topic: topic)
                    }
                }
            }
            .padding()
        }
    }

    private var sampleExamplesView: some View {
        VStack(spacing: 15) {
            Text("Examples")
                .font(.headline)
                .fontWeight(.semibold)

            ExampleCard(
                title: "Example 1: Basic Concept",
                description: "Understanding the fundamental principles",
                content: "This example demonstrates the core concept with step-by-step explanation."
            )

            ExampleCard(
                title: "Example 2: Practical Application",
                description: "Real-world usage and application",
                content: "Here's how you would apply this concept in everyday situations."
            )
        }
    }

    private var sampleQuizzesView: some View {
        VStack(spacing: 15) {
            Text("Practice Quizzes")
                .font(.headline)
                .fontWeight(.semibold)

            QuizCard(
                title: "Quick Check Quiz",
                description: "Test your understanding with 5 questions",
                questionCount: 5,
                timeLimit: 10
            )

            QuizCard(
                title: "Comprehensive Assessment",
                description: "Complete evaluation of lesson concepts",
                questionCount: 15,
                timeLimit: 25
            )
        }
    }

    private var sampleExercisesView: some View {
        VStack(spacing: 15) {
            Text("Practice Exercises")
                .font(.headline)
                .fontWeight(.semibold)

            ExerciseCard(
                title: "Practice Problem Set A",
                description: "Basic problems to reinforce learning",
                exerciseCount: 8,
                difficulty: .beginner
            )

            ExerciseCard(
                title: "Challenge Problems",
                description: "Advanced problems for deeper understanding",
                exerciseCount: 5,
                difficulty: .advanced
            )
        }
    }

    private func getSubjectForLesson() -> String {
        // This would normally look up the subject from the lesson's subjectId
        // For now, return a default based on lesson content
        if lesson.title.contains("Math") {
            return "Math Teacher"
        } else if lesson.title.contains("Science") {
            return "Science Teacher"
        } else if lesson.title.contains("Reading") {
            return "Reading Teacher"
        } else {
            return "Learning Coach"
        }
    }
}

// MARK: - Supporting Views

struct TopicExamplesView: View {
    let topic: LessonTopic

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(topic.title)
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(topic.examples) { example in
                ExampleCard(
                    title: example.title,
                    description: example.description,
                    content: example.content
                )
            }
        }
    }
}

struct TopicQuizzesView: View {
    let topic: LessonTopic

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("\(topic.title) - Quizzes")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(topic.quizzes) { quiz in
                QuizCard(
                    title: quiz.title,
                    description: quiz.description,
                    questionCount: quiz.questions.count,
                    timeLimit: quiz.timeLimit
                )
            }
        }
    }
}

struct TopicExercisesView: View {
    let topic: LessonTopic

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("\(topic.title) - Exercises")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(topic.exercises) { exercise in
                ExerciseCard(
                    title: exercise.title,
                    description: exercise.description,
                    exerciseCount: 1,
                    difficulty: exercise.difficulty
                )
            }
        }
    }
}

struct ExampleCard: View {
    let title: String
    let description: String
    let content: String

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            Text(content)
                .font(.subheadline)
                .foregroundColor(.primary)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct QuizCard: View {
    let title: String
    let description: String
    let questionCount: Int
    let timeLimit: Int

    var body: some View {
        Button(action: {
            // Start quiz
        }) {
            HStack(spacing: 15) {
                Image(systemName: "questionmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    HStack {
                        Label("\(questionCount) questions", systemImage: "list.bullet")
                        Label("\(timeLimit) min", systemImage: "clock")
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }

                Spacer()

                Text("Start")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.blue)
                    .cornerRadius(8)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ExerciseCard: View {
    let title: String
    let description: String
    let exerciseCount: Int
    let difficulty: DifficultyLevel

    var body: some View {
        Button(action: {
            // Start exercise
        }) {
            HStack(spacing: 15) {
                Image(systemName: "pencil.circle.fill")
                    .font(.title2)
                    .foregroundColor(.green)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    HStack {
                        Label("\(exerciseCount) exercises", systemImage: "list.number")
                        Label(difficulty.displayName, systemImage: "chart.bar")
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }

                Spacer()

                Text("Practice")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.green)
                    .cornerRadius(8)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    LessonDetailView(
        lesson: Lesson(
            title: "Introduction to Fractions",
            description: "Learn about fractions and how to work with them",
            subjectId: UUID(),
            gradeLevel: .grade3,
            duration: 45
        )
    )
}