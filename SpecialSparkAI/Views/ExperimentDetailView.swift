//
//  ExperimentDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct ExperimentDetailView: View {
    let experiment: ScienceExperiment
    let gradeLevel: GradeLevel
    @State private var selectedTab: ExperimentTab = .overview
    @State private var showingAIChat = false
    @Environment(\.dismiss) private var dismiss

    enum ExperimentTab: String, CaseIterable {
        case overview = "Overview"
        case materials = "Materials"
        case procedure = "Procedure"
        case results = "Results"

        var icon: String {
            switch self {
            case .overview: return "info.circle.fill"
            case .materials: return "list.bullet.rectangle"
            case .procedure: return "list.number"
            case .results: return "chart.bar.fill"
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Experiment Header
                experimentHeaderView

                // Tab Selection
                tabSelectionView

                // Content Area
                TabView(selection: $selectedTab) {
                    overviewView
                        .tag(ExperimentTab.overview)

                    materialsView
                        .tag(ExperimentTab.materials)

                    procedureView
                        .tag(ExperimentTab.procedure)

                    resultsView
                        .tag(ExperimentTab.results)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(experiment.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingAIChat = true
                    }) {
                        Image(systemName: "message.circle.fill")
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .sheet(isPresented: $showingAIChat) {
            AITeacherChatView(subject: "Science Teacher")
        }
    }

    private var experimentHeaderView: some View {
        VStack(spacing: 15) {
            // Experiment icon and domain
            HStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(
                        colors: [experiment.scienceDomain.color.opacity(0.3), experiment.scienceDomain.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: experiment.scienceDomain.icon)
                            .foregroundColor(.white)
                            .font(.title2)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(experiment.scienceDomain.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(experiment.scienceDomain.color)

                    Text(experiment.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                Spacer()
            }

            // Experiment metadata
            HStack(spacing: 20) {
                ExperimentMetadataView(title: "Duration", value: "\(experiment.duration) min", icon: "clock")
                ExperimentMetadataView(title: "Difficulty", value: experiment.difficulty.displayName, icon: "chart.bar")
                ExperimentMetadataView(title: "Safety", value: experiment.safetyLevel.displayName, icon: "shield", color: experiment.safetyLevel.color)
            }

            // Safety warning if needed
            if experiment.safetyLevel == .adultSupervision || experiment.safetyLevel == .high {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)

                    Text("Adult supervision required for this experiment")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .fontWeight(.medium)

                    Spacer()
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
    }

    private var tabSelectionView: some View {
        HStack(spacing: 0) {
            ForEach(ExperimentTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.title3)

                        Text(tab.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .green : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
            }
        }
        .background(Color(.systemGray6))
        .overlay(
            Rectangle()
                .fill(.green)
                .frame(height: 2)
                .offset(x: tabOffset, y: 0)
                .animation(.easeInOut(duration: 0.3), value: selectedTab),
            alignment: .bottom
        )
    }

    private var tabOffset: CGFloat {
        let tabWidth = UIScreen.main.bounds.width / CGFloat(ExperimentTab.allCases.count)
        let index = ExperimentTab.allCases.firstIndex(of: selectedTab) ?? 0
        return (CGFloat(index) * tabWidth) - (UIScreen.main.bounds.width / 2) + (tabWidth / 2)
    }

    private var overviewView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Objective
                SectionCard(title: "Objective", icon: "target") {
                    Text(experiment.objective.isEmpty ? "Learn about \(experiment.scienceDomain.displayName.lowercased()) through hands-on experimentation." : experiment.objective)
                        .font(.subheadline)
                }

                // Hypothesis
                SectionCard(title: "Hypothesis", icon: "lightbulb") {
                    Text(experiment.hypothesis.isEmpty ? "What do you think will happen in this experiment? Write your prediction before starting." : experiment.hypothesis)
                        .font(.subheadline)
                }

                // Learning Goals
                SectionCard(title: "What You'll Learn", icon: "graduationcap") {
                    VStack(alignment: .leading, spacing: 8) {
                        LearningGoalItem(text: "Understand \(experiment.scienceDomain.displayName.lowercased()) concepts")
                        LearningGoalItem(text: "Practice scientific observation skills")
                        LearningGoalItem(text: "Learn to record and analyze data")
                        LearningGoalItem(text: "Develop critical thinking abilities")
                    }
                }

                // Virtual Simulation (if available)
                if experiment.isVirtual {
                    SectionCard(title: "Virtual Simulation", icon: "play.rectangle") {
                        VStack(spacing: 12) {
                            Text("Try the virtual version first!")
                                .font(.subheadline)
                                .fontWeight(.medium)

                            Button(action: {
                                // Launch virtual simulation
                            }) {
                                HStack {
                                    Image(systemName: "play.fill")
                                    Text("Start Virtual Experiment")
                                }
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .padding()
                                .background(.blue)
                                .cornerRadius(8)
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }

    private var materialsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Required materials
                SectionCard(title: "Required Materials", icon: "list.bullet.rectangle") {
                    if experiment.materials.isEmpty {
                        VStack(spacing: 8) {
                            MaterialItem(name: "Clear containers", quantity: "2-3", isOptional: false)
                            MaterialItem(name: "Water", quantity: "500ml", isOptional: false)
                            MaterialItem(name: "Measuring tools", quantity: "1 set", isOptional: false)
                            MaterialItem(name: "Notebook", quantity: "1", isOptional: false)
                            MaterialItem(name: "Pencil", quantity: "1", isOptional: false)
                        }
                    } else {
                        VStack(spacing: 8) {
                            ForEach(experiment.materials) { material in
                                MaterialItem(
                                    name: material.name,
                                    quantity: material.quantity,
                                    isOptional: material.isOptional
                                )
                            }
                        }
                    }
                }

                // Safety equipment
                SectionCard(title: "Safety Equipment", icon: "shield") {
                    VStack(spacing: 8) {
                        SafetyEquipmentItem(name: "Safety goggles", required: experiment.safetyLevel != .low)
                        SafetyEquipmentItem(name: "Gloves", required: experiment.safetyLevel == .high)
                        SafetyEquipmentItem(name: "Apron", required: experiment.safetyLevel != .low)
                        SafetyEquipmentItem(name: "Adult supervision", required: experiment.safetyLevel == .adultSupervision)
                    }
                }

                // Preparation tips
                SectionCard(title: "Preparation Tips", icon: "checkmark.circle") {
                    VStack(alignment: .leading, spacing: 8) {
                        PreparationTip(text: "Read all instructions carefully before starting")
                        PreparationTip(text: "Gather all materials in one place")
                        PreparationTip(text: "Ensure you have adequate workspace")
                        PreparationTip(text: "Have your observation notebook ready")
                    }
                }
            }
            .padding()
        }
    }

    private var procedureView: some View {
        ScrollView {
            VStack(spacing: 20) {
                SectionCard(title: "Step-by-Step Procedure", icon: "list.number") {
                    if experiment.procedures.isEmpty {
                        VStack(spacing: 12) {
                            ProcedureStep(number: 1, instruction: "Set up your workspace and gather all materials")
                            ProcedureStep(number: 2, instruction: "Put on appropriate safety equipment")
                            ProcedureStep(number: 3, instruction: "Follow the experimental steps carefully")
                            ProcedureStep(number: 4, instruction: "Record your observations in your notebook")
                            ProcedureStep(number: 5, instruction: "Clean up your workspace when finished")
                        }
                    } else {
                        VStack(spacing: 12) {
                            ForEach(experiment.procedures) { procedure in
                                ProcedureStep(
                                    number: procedure.stepNumber,
                                    instruction: procedure.instruction
                                )
                            }
                        }
                    }
                }

                // Observation points
                SectionCard(title: "What to Observe", icon: "eye") {
                    VStack(alignment: .leading, spacing: 8) {
                        ObservationPoint(text: "Changes in color, texture, or appearance")
                        ObservationPoint(text: "Temperature changes")
                        ObservationPoint(text: "Sounds or reactions")
                        ObservationPoint(text: "Time taken for changes to occur")
                        ObservationPoint(text: "Any unexpected results")
                    }
                }
            }
            .padding()
        }
    }

    private var resultsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Expected results
                SectionCard(title: "Expected Results", icon: "chart.bar") {
                    Text(experiment.expectedResults.isEmpty ? "Record what you observe during the experiment. Compare your results with your initial hypothesis." : experiment.expectedResults)
                        .font(.subheadline)
                }

                // Data recording template
                SectionCard(title: "Record Your Data", icon: "doc.text") {
                    VStack(spacing: 12) {
                        DataRecordingField(title: "Hypothesis", placeholder: "What do you think will happen?")
                        DataRecordingField(title: "Observations", placeholder: "What did you see, hear, or notice?")
                        DataRecordingField(title: "Results", placeholder: "What actually happened?")
                        DataRecordingField(title: "Conclusion", placeholder: "Was your hypothesis correct? What did you learn?")
                    }
                }

                // Explanation
                SectionCard(title: "Scientific Explanation", icon: "brain") {
                    Text(experiment.explanation.isEmpty ? "This experiment demonstrates important scientific principles. Discuss your results with your teacher to understand the science behind what you observed." : experiment.explanation)
                        .font(.subheadline)
                }

                // Extensions
                if !experiment.extensions.isEmpty {
                    SectionCard(title: "Try This Next", icon: "arrow.right.circle") {
                        VStack(alignment: .leading, spacing: 8) {
                            ForEach(experiment.extensions, id: \.self) { extension in
                                ExtensionItem(text: extension)
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }
}

// MARK: - Supporting Views

struct ExperimentMetadataView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color?

    init(title: String, value: String, icon: String, color: Color? = nil) {
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
    }

    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color ?? .green)

            Text(value)
                .font(.caption)
                .fontWeight(.bold)

            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct SectionCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content

    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.green)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct LearningGoalItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct MaterialItem: View {
    let name: String
    let quantity: String
    let isOptional: Bool

    var body: some View {
        HStack {
            Image(systemName: isOptional ? "circle" : "circle.fill")
                .foregroundColor(isOptional ? .gray : .green)
                .font(.caption)

            Text(name)
                .font(.subheadline)
                .foregroundColor(isOptional ? .secondary : .primary)

            Spacer()

            Text(quantity)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(Color(.systemGray6))
                .cornerRadius(4)

            if isOptional {
                Text("Optional")
                    .font(.caption2)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(4)
            }
        }
    }
}

struct SafetyEquipmentItem: View {
    let name: String
    let required: Bool

    var body: some View {
        HStack {
            Image(systemName: required ? "exclamationmark.triangle.fill" : "checkmark.circle")
                .foregroundColor(required ? .orange : .green)
                .font(.caption)

            Text(name)
                .font(.subheadline)
                .foregroundColor(required ? .primary : .secondary)

            Spacer()

            Text(required ? "Required" : "Optional")
                .font(.caption2)
                .foregroundColor(required ? .orange : .green)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background((required ? Color.orange : Color.green).opacity(0.1))
                .cornerRadius(4)
        }
    }
}

struct PreparationTip: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "lightbulb.fill")
                .foregroundColor(.yellow)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct ProcedureStep: View {
    let number: Int
    let instruction: String

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(number)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 30, height: 30)
                .background(.green)
                .clipShape(Circle())

            Text(instruction)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()
        }
    }
}

struct ObservationPoint: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "eye.fill")
                .foregroundColor(.blue)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct DataRecordingField: View {
    let title: String
    let placeholder: String
    @State private var text = ""

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)

            TextEditor(text: $text)
                .font(.caption)
                .frame(minHeight: 60)
                .padding(8)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .overlay(
                    Text(placeholder)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(12)
                        .opacity(text.isEmpty ? 1 : 0),
                    alignment: .topLeading
                )
        }
    }
}

struct ExtensionItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "arrow.right.circle.fill")
                .foregroundColor(.purple)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

#Preview {
    ExperimentDetailView(
        experiment: ScienceExperiment(
            title: "Volcano Eruption",
            description: "Create a safe volcano eruption using baking soda and vinegar",
            objective: "Learn about chemical reactions and volcanic activity",
            gradeLevel: .grade4,
            domain: .chemistry
        ),
        gradeLevel: .grade4
    )
}
