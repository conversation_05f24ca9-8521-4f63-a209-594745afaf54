//
//  VirtualCampusView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct VirtualCampusView: View {
    let student: StudentProfile?
    @StateObject private var lessonDataService = LessonDataService()
    @StateObject private var settingsManager = SettingsManager()
    @State private var selectedSubject: Subject?
    @State private var showingSubjectDetail = false
    @State private var showingReadingLibrary = false
    @State private var showingScienceLabs = false
    @State private var showingArtCenter = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Lessons Header
                    lessonsHeaderView

                    // Quick Stats
                    quickStatsView

                    // Available Courses Grid
                    availableCoursesView

                    // Recommended Reading
                    recommendedReadingView

                    // Recent Activities
                    recentActivitiesView
                }
                .padding()
            }
            .navigationTitle("Lessons")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingSubjectDetail) {
            if let subject = selectedSubject {
                SubjectDetailView(subject: subject, gradeLevel: currentGradeLevel)
            }
        }
        .sheet(isPresented: $showingReadingLibrary) {
            ReadingLibraryView(gradeLevel: currentGradeLevel)
        }
        .sheet(isPresented: $showingScienceLabs) {
            ScienceLabsView(gradeLevel: currentGradeLevel)
        }
        .sheet(isPresented: $showingArtCenter) {
            ArtCenterView(gradeLevel: currentGradeLevel)
        }
    }

    private var currentGradeLevel: GradeLevel {
        return settingsManager.selectedGradeLevel
    }

    private var filteredSubjects: [Subject] {
        return SubjectDataService().getSubjectsForGrade(currentGradeLevel)
    }

    private var lessonsHeaderView: some View {
        VStack(spacing: 15) {
            // Welcome message
            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text("Welcome to")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text("Your Learning Journey")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple, .pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Grade \(currentGradeLevel.displayName) • Personalized for you")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Learning icon
                Image(systemName: "book.circle.fill")
                    .font(.system(size: 60))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }

            // Quick progress stats
            HStack(spacing: 20) {
                LessonStatView(title: "Available Courses", value: "\(filteredSubjects.count)", icon: "books.vertical")
                LessonStatView(title: "Lessons Today", value: "3", icon: "calendar")
                LessonStatView(title: "Progress", value: "85%", icon: "chart.line.uptrend.xyaxis")
                LessonStatView(title: "Achievements", value: "12", icon: "star.fill")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 5)
        )
    }

    private var quickStatsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Today's Learning Overview")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 15) {
                // Today's Schedule
                VStack(alignment: .leading, spacing: 8) {
                    Label("Next Lesson", systemImage: "clock")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("Math - Fractions")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("in 15 minutes")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)

                Spacer()

                // Weekly Progress
                VStack(alignment: .leading, spacing: 8) {
                    Label("This Week", systemImage: "chart.bar")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("8 of 10")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("lessons completed")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }

    private var availableCoursesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Available Courses")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                // Academic Center - All Subjects
                CourseAreaCard(
                    title: "Academic Center",
                    description: "All subjects for Grade \(currentGradeLevel.displayName)",
                    icon: "building.2.fill",
                    color: .blue,
                    courseCount: filteredSubjects.count,
                    totalLessons: lessonDataService.lessons.filter { $0.gradeLevel == currentGradeLevel }.count
                ) {
                    // Navigate to academic center with all subjects
                    showingSubjectDetail = true
                }

                // Reading Library
                CourseAreaCard(
                    title: "Recommended Books",
                    description: "Reading activities and book tracking",
                    icon: "books.vertical.fill",
                    color: .purple,
                    courseCount: lessonDataService.getBooksForGrade(currentGradeLevel).count,
                    totalLessons: lessonDataService.getRecommendedBooks(for: currentGradeLevel).count
                ) {
                    showingReadingLibrary = true
                }

                // Science Labs
                CourseAreaCard(
                    title: "Science Labs",
                    description: "Experiments for Grade \(currentGradeLevel.displayName)",
                    icon: "flask.fill",
                    color: .green,
                    courseCount: lessonDataService.getExperimentsForGrade(currentGradeLevel).count,
                    totalLessons: lessonDataService.getExperimentsForGrade(currentGradeLevel).count
                ) {
                    showingScienceLabs = true
                }

                // Creative Arts Center
                CourseAreaCard(
                    title: "Creative Arts Center",
                    description: "Art projects and creative activities",
                    icon: "paintbrush.fill",
                    color: .pink,
                    courseCount: lessonDataService.getArtProjectsForGrade(currentGradeLevel).count,
                    totalLessons: lessonDataService.getArtProjectsForGrade(currentGradeLevel).count
                ) {
                    showingArtCenter = true
                }
            }
        }
    }

    private var recommendedReadingView: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Recommended Books")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    showingReadingLibrary = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(lessonDataService.getRecommendedBooks(for: currentGradeLevel, limit: 5)) { book in
                        BookCard(book: book)
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private var recentActivitiesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Recent Learning Activities")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 10) {
                LearningActivityCard(
                    title: "Math Lesson Completed",
                    subject: "Mathematics",
                    time: "2 hours ago",
                    progress: 95,
                    icon: "function",
                    color: .blue
                )

                LearningActivityCard(
                    title: "Science Experiment",
                    subject: "Biology",
                    time: "Yesterday",
                    progress: 80,
                    icon: "flask.fill",
                    color: .green
                )

                LearningActivityCard(
                    title: "Reading Assignment",
                    subject: "Literature",
                    time: "2 days ago",
                    progress: 100,
                    icon: "book.fill",
                    color: .purple
                )
            }
        }
    }
}

struct LessonStatView: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct CourseAreaCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let courseCount: Int
    let totalLessons: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                // Course count indicator
                HStack {
                    Spacer()
                    HStack(spacing: 5) {
                        Circle()
                            .fill(color)
                            .frame(width: 8, height: 8)
                        Text("\(courseCount) courses")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Icon
                Image(systemName: icon)
                    .font(.largeTitle)
                    .foregroundColor(color)

                // Content
                VStack(spacing: 8) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    Text("\(totalLessons) lessons available")
                        .font(.caption2)
                        .foregroundColor(color)
                }

                // Action button
                Text("Explore")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                    .background(color)
                    .cornerRadius(15)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct BookCard: View {
    let book: Book

    var body: some View {
        VStack(spacing: 12) {
            // Book cover placeholder
            RoundedRectangle(cornerRadius: 8)
                .fill(LinearGradient(
                    colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 80, height: 100)
                .overlay(
                    Image(systemName: "book.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                )

            // Book info
            VStack(spacing: 4) {
                Text(book.title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)

                Text(book.author)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)

                Text("\(book.pageCount) pages")
                    .font(.caption2)
                    .foregroundColor(.blue)
            }
        }
        .frame(width: 100)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct LearningActivityCard: View {
    let title: String
    let subject: String
    let time: String
    let progress: Int
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 15) {
            // Icon
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)

            // Content
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                HStack {
                    Label(subject, systemImage: "book")
                    Spacer()
                    Label(time, systemImage: "clock")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }

            // Progress
            VStack {
                Text("\(progress)%")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(progress == 100 ? .green : .primary)

                Text("complete")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

// CampusAreaCard and BuildingDetailView are defined in CampusComponents.swift

#Preview {
    VirtualCampusView(student: nil)
}
