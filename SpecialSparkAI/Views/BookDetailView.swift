//
//  BookDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct BookDetailView: View {
    let book: Book
    let gradeLevel: GradeLevel
    @State private var isReading = false
    @State private var currentPage = 0
    @State private var readingGoal: ReadingGoal = .enjoyment
    @State private var showingReadingSession = false
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Book Header
                    bookHeaderView

                    // Book Details
                    bookDetailsView

                    // Reading Actions
                    readingActionsView

                    // Reading Progress (if started)
                    if isReading {
                        readingProgressView
                    }

                    // Related Activities
                    relatedActivitiesView
                }
                .padding()
            }
            .navigationTitle(book.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingReadingSession) {
            ReadingSessionView(book: book, currentPage: $currentPage)
        }
    }

    private var bookHeaderView: some View {
        VStack(spacing: 20) {
            // Book cover
            RoundedRectangle(cornerRadius: 12)
                .fill(LinearGradient(
                    colors: [book.genre.color.opacity(0.3), book.genre.color.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 150, height: 200)
                .overlay(
                    VStack {
                        if book.isRecommended {
                            HStack {
                                Spacer()
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.title3)
                            }
                            .padding(8)
                        }

                        Spacer()

                        Image(systemName: "book.fill")
                            .foregroundColor(.white)
                            .font(.system(size: 40))

                        Spacer()
                    }
                )
                .shadow(radius: 5)

            // Book info
            VStack(spacing: 8) {
                Text(book.title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)

                Text("by \(book.author)")
                    .font(.headline)
                    .foregroundColor(.secondary)

                HStack(spacing: 15) {
                    Label(book.genre.displayName, systemImage: "tag")
                    Label("\(book.pageCount) pages", systemImage: "doc.text")
                    Label("\(book.estimatedReadingTime) min", systemImage: "clock")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
        }
    }

    private var bookDetailsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("About This Book")
                .font(.headline)
                .fontWeight(.semibold)

            if !book.description.isEmpty {
                Text(book.description)
                    .font(.subheadline)
                    .foregroundColor(.primary)
            } else {
                Text("A wonderful book perfect for Grade \(gradeLevel.displayName) readers. This \(book.genre.displayName.lowercased()) will engage and inspire young minds.")
                    .font(.subheadline)
                    .foregroundColor(.primary)
            }

            // Book metadata
            VStack(spacing: 12) {
                BookMetadataRow(title: "Reading Level", value: book.readingLevel.displayName)
                BookMetadataRow(title: "Grade Level", value: "Grade \(book.gradeLevel.displayName)")
                BookMetadataRow(title: "Language", value: book.language)
                if let publisher = book.publisher {
                    BookMetadataRow(title: "Publisher", value: publisher)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    private var readingActionsView: some View {
        VStack(spacing: 15) {
            if !isReading {
                // Start reading section
                VStack(spacing: 12) {
                    Text("Start Reading")
                        .font(.headline)
                        .fontWeight(.semibold)

                    // Reading goal selection
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Reading Goal")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Picker("Reading Goal", selection: $readingGoal) {
                            ForEach(ReadingGoal.allCases, id: \.self) { goal in
                                Text(goal.displayName).tag(goal)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }

                    Button(action: {
                        startReading()
                    }) {
                        HStack {
                            Image(systemName: "play.fill")
                            Text("Start Reading")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.blue)
                        .cornerRadius(12)
                    }
                }
            } else {
                // Continue reading section
                VStack(spacing: 12) {
                    Text("Continue Reading")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Button(action: {
                        showingReadingSession = true
                    }) {
                        HStack {
                            Image(systemName: "book.open")
                            Text("Continue from page \(currentPage)")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.green)
                        .cornerRadius(12)
                    }
                }
            }
        }
    }

    private var readingProgressView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Reading Progress")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                // Progress bar
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Page \(currentPage) of \(book.pageCount)")
                            .font(.subheadline)

                        Spacer()

                        Text("\(Int((Double(currentPage) / Double(book.pageCount)) * 100))%")
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                    }

                    ProgressView(value: Double(currentPage), total: Double(book.pageCount))
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                }

                // Reading stats
                HStack(spacing: 20) {
                    ReadingStatCard(title: "Pages Read", value: "\(currentPage)", icon: "doc.text")
                    ReadingStatCard(title: "Time Spent", value: "2h 15m", icon: "clock")
                    ReadingStatCard(title: "Sessions", value: "3", icon: "calendar")
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    private var relatedActivitiesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Related Activities")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                RelatedActivityCard(
                    title: "Vocabulary Quiz",
                    description: "Test your understanding of new words",
                    icon: "textformat.abc",
                    color: .purple,
                    isAvailable: isReading && currentPage > 10
                )

                RelatedActivityCard(
                    title: "Comprehension Questions",
                    description: "Answer questions about the story",
                    icon: "questionmark.circle",
                    color: .blue,
                    isAvailable: isReading && currentPage > 20
                )

                RelatedActivityCard(
                    title: "Character Analysis",
                    description: "Explore the main characters",
                    icon: "person.2",
                    color: .green,
                    isAvailable: isReading && currentPage > 30
                )

                RelatedActivityCard(
                    title: "Book Report",
                    description: "Write a summary of your reading",
                    icon: "doc.text",
                    color: .orange,
                    isAvailable: currentPage >= book.pageCount
                )
            }
        }
    }

    private func startReading() {
        isReading = true
        currentPage = 1
        showingReadingSession = true
    }
}

// MARK: - Supporting Views

struct BookMetadataRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
    }
}

struct ReadingStatCard: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct RelatedActivityCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let isAvailable: Bool

    var body: some View {
        Button(action: {
            // Navigate to activity
        }) {
            HStack(spacing: 15) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isAvailable ? color : .gray)
                    .frame(width: 40)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(isAvailable ? .primary : .secondary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if isAvailable {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    Image(systemName: "lock.fill")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isAvailable ? Color(.systemBackground) : Color(.systemGray6))
                    .shadow(radius: isAvailable ? 2 : 0)
            )
        }
        .disabled(!isAvailable)
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    BookDetailView(
        book: Book(
            title: "Charlotte's Web",
            author: "E.B. White",
            gradeLevel: .grade3,
            genre: .fiction,
            pageCount: 184
        ),
        gradeLevel: .grade3
    )
}
