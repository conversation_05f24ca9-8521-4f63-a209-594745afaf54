//
//  EnhancedSensorySettingsView.swift
//  SpecialSparkAI
//
//  Phase 2: Enhanced Sensory Settings for Special Needs Support
//

import SwiftUI

// MARK: - Sensory Presets
enum SensoryPreset: String, CaseIterable {
    case autism = "autism"
    case adhd = "adhd"
    case dyslexia = "dyslexia"
    case visualImpairment = "visual_impairment"
    case hearingImpairment = "hearing_impairment"
    case motorImpairment = "motor_impairment"
    case anxiety = "anxiety"
    case standard = "standard"
    
    var displayName: String {
        switch self {
        case .autism: return "Autism Support"
        case .adhd: return "ADHD Support"
        case .dyslexia: return "Dyslexia Support"
        case .visualImpairment: return "Visual Support"
        case .hearingImpairment: return "Hearing Support"
        case .motorImpairment: return "Motor Support"
        case .anxiety: return "Anxiety Support"
        case .standard: return "Standard"
        }
    }
    
    var description: String {
        switch self {
        case .autism: return "Reduced sensory input, predictable interface"
        case .adhd: return "Minimal distractions, focus aids"
        case .dyslexia: return "Reading support, clear fonts"
        case .visualImpairment: return "High contrast, large text"
        case .hearingImpairment: return "Visual cues, captions"
        case .motorImpairment: return "Large targets, longer timeouts"
        case .anxiety: return "Calming colors, gentle interactions"
        case .standard: return "Default settings for all users"
        }
    }
    
    var icon: String {
        switch self {
        case .autism: return "brain.head.profile"
        case .adhd: return "bolt.circle"
        case .dyslexia: return "textformat"
        case .visualImpairment: return "eye.circle"
        case .hearingImpairment: return "ear.circle"
        case .motorImpairment: return "hand.point.up.left.circle"
        case .anxiety: return "heart.circle"
        case .standard: return "gear.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .autism: return .blue
        case .adhd: return .orange
        case .dyslexia: return .green
        case .visualImpairment: return .purple
        case .hearingImpairment: return .red
        case .motorImpairment: return .brown
        case .anxiety: return .pink
        case .standard: return .gray
        }
    }
}

struct EnhancedSensorySettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @StateObject private var mockAuthService = MockAuthService.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedPreset: SensoryPreset?
    @State private var showingCalmMode = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Quick Presets
                    presetsSection
                    
                    // Detailed Settings
                    detailedSettingsSection
                    
                    // Emergency Features
                    emergencySection
                }
                .padding()
            }
            .navigationTitle("Sensory Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Reset") {
                        resetToDefaults()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveSettings()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCalmMode) {
            CalmModeView()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "heart.circle.fill")
                .font(.system(size: 50))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.pink, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text("Sensory Comfort Settings")
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("Customize your learning environment for maximum comfort and focus")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Presets Section
    private var presetsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Setup")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(SensoryPreset.allCases, id: \.self) { preset in
                    PresetCard(
                        preset: preset,
                        isSelected: selectedPreset == preset
                    ) {
                        applyPreset(preset)
                    }
                }
            }
        }
    }
    
    // MARK: - Detailed Settings Section
    private var detailedSettingsSection: some View {
        VStack(spacing: 20) {
            // Visual Settings
            SettingsGroup(title: "Visual Comfort", icon: "eye.fill", color: .blue) {
                VStack(spacing: 12) {
                    SettingsToggle(
                        title: "High Contrast",
                        subtitle: "Increases text and background contrast",
                        isOn: $settingsManager.highContrastMode
                    )
                    
                    SettingsToggle(
                        title: "Reduce Motion",
                        subtitle: "Minimizes animations and movement",
                        isOn: $settingsManager.reduceMotion
                    )
                    
                    SettingsToggle(
                        title: "Large Text",
                        subtitle: "Increases text size for better readability",
                        isOn: $settingsManager.largeText
                    )
                }
            }
            
            // Audio Settings
            SettingsGroup(title: "Audio Comfort", icon: "speaker.wave.2.fill", color: .green) {
                VStack(spacing: 12) {
                    SettingsToggle(
                        title: "Mute All Sounds",
                        subtitle: "Turns off all audio feedback",
                        isOn: $settingsManager.muteAllSounds
                    )
                    
                    SettingsToggle(
                        title: "Audio Descriptions",
                        subtitle: "Spoken descriptions of visual elements",
                        isOn: $settingsManager.enableAudioDescriptions
                    )
                }
            }
            
            // Motion Settings
            SettingsGroup(title: "Motion & Animation", icon: "slowmo", color: .orange) {
                VStack(spacing: 12) {
                    SettingsToggle(
                        title: "Disable Auto-Play",
                        subtitle: "Prevents automatic animations",
                        isOn: $settingsManager.disableAutoPlay
                    )
                    
                    SettingsToggle(
                        title: "Slower Transitions",
                        subtitle: "Makes screen changes more gradual",
                        isOn: $settingsManager.slowerTransitions
                    )
                }
            }
        }
    }
    
    // MARK: - Emergency Section
    private var emergencySection: some View {
        SettingsGroup(title: "Emergency & Calm", icon: "heart.circle.fill", color: .red) {
            VStack(spacing: 12) {
                Button("Activate Calm Mode") {
                    showingCalmMode = true
                }
                .buttonStyle(.borderedProminent)
                .frame(maxWidth: .infinity)
                
                Text("Quick access to calming features when feeling overwhelmed")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    // MARK: - Actions
    private func applyPreset(_ preset: SensoryPreset) {
        selectedPreset = preset
        settingsManager.applyPreset(preset)
    }
    
    private func resetToDefaults() {
        settingsManager.resetToDefaults()
        selectedPreset = nil
    }
    
    private func saveSettings() {
        settingsManager.saveSettings()
    }
}

// MARK: - Preset Card
struct PresetCard: View {
    let preset: SensoryPreset
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: preset.icon)
                    .font(.title2)
                    .foregroundColor(preset.color)
                
                Text(preset.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                
                Text(preset.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .padding(12)
            .frame(maxWidth: .infinity, minHeight: 100)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? preset.color.opacity(0.2) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? preset.color : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Settings Group
struct SettingsGroup<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    @ViewBuilder let content: Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

// MARK: - Settings Toggle
struct SettingsToggle: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
    }
}

// MARK: - Calm Mode View
struct CalmModeView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var breathingPhase = 0
    
    var body: some View {
        ZStack {
            // Calming gradient background
            LinearGradient(
                colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                Text("Take a Deep Breath")
                    .font(.title)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                // Breathing animation
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [.blue.opacity(0.6), .blue.opacity(0.2)],
                            center: .center,
                            startRadius: 20,
                            endRadius: 100
                        )
                    )
                    .frame(width: 150, height: 150)
                    .scaleEffect(breathingPhase == 0 ? 0.8 : 1.2)
                    .animation(
                        Animation.easeInOut(duration: 4)
                            .repeatForever(autoreverses: true),
                        value: breathingPhase
                    )
                
                VStack(spacing: 8) {
                    Text("Breathe in... and out...")
                        .font(.headline)
                        .multilineTextAlignment(.center)
                    
                    Text("You're safe and doing great")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                Button("I'm Feeling Better") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .padding(.top, 20)
            }
            .padding()
        }
        .onAppear {
            breathingPhase = 1
        }
    }
}

#Preview {
    EnhancedSensorySettingsView()
}
