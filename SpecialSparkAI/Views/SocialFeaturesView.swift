//
//  SocialFeaturesView.swift
//  SpecialSparkAI
//
//  Phase 4: Safe Social Features Interface
//

import SwiftUI

struct SocialFeaturesView: View {
    @StateObject private var socialService = SocialFeaturesService.shared
    @StateObject private var mockAuthService = MockAuthService.shared

    @State private var selectedTab = 0
    @State private var showingCreateGroup = false
    @State private var showingJoinActivity = false
    @State private var selectedGroup: StudyGroup?
    @State private var selectedActivity: CollaborativeActivity?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView

                // Safety Notice
                safetyNoticeView

                // Tab Selection
                tabSelector

                // Content
                TabView(selection: $selectedTab) {
                    // Study Groups
                    studyGroupsView
                        .tag(0)

                    // Collaborative Activities
                    collaborativeActivitiesView
                        .tag(1)

                    // Peer Connections
                    peerConnectionsView
                        .tag(2)

                    // Safe Messages
                    safeMessagesView
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Social Learning")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingCreateGroup = true }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
        }
        .sheet(isPresented: $showingCreateGroup) {
            CreateStudyGroupView()
        }
        .sheet(isPresented: $showingJoinActivity) {
            if let activity = selectedActivity {
                CollaborativeActivityDetailView(activity: activity)
            }
        }
    }

    // MARK: - Header View

    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Social Learning")
                        .font(.title2)
                        .fontWeight(.bold)

                    if let student = mockAuthService.currentStudent {
                        Text("Connect safely with \(student.firstName)!")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Quick Stats
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(socialService.studyGroups.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("Groups")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    // MARK: - Safety Notice

    private var safetyNoticeView: some View {
        HStack(spacing: 12) {
            Image(systemName: "shield.checkered")
                .font(.title3)
                .foregroundColor(.green)

            VStack(alignment: .leading, spacing: 4) {
                Text("Safe Learning Environment")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text("All interactions are monitored and moderated for safety")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.green.opacity(0.1))
        )
        .padding(.horizontal)
    }

    // MARK: - Tab Selector

    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(0..<4) { index in
                Button(action: { selectedTab = index }) {
                    VStack(spacing: 4) {
                        Image(systemName: tabIcon(for: index))
                            .font(.title3)

                        Text(tabTitle(for: index))
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == index ? .blue : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
        .padding(.top, 8)
    }

    // MARK: - Study Groups View

    private var studyGroupsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(socialService.studyGroups) { group in
                    StudyGroupCard(group: group) {
                        selectedGroup = group
                        Task {
                            await socialService.joinStudyGroup(group.id)
                        }
                    }
                }

                if socialService.studyGroups.isEmpty {
                    EmptyStateView(
                        icon: "person.3",
                        title: "No Study Groups",
                        message: "Create or join a study group to start learning with peers!"
                    )
                }
            }
            .padding()
        }
    }

    // MARK: - Collaborative Activities View

    private var collaborativeActivitiesView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(socialService.collaborativeActivities) { activity in
                    CollaborativeActivityCard(activity: activity) {
                        selectedActivity = activity
                        showingJoinActivity = true
                    }
                }

                if socialService.collaborativeActivities.isEmpty {
                    EmptyStateView(
                        icon: "rectangle.3.group",
                        title: "No Activities",
                        message: "Join a study group to participate in collaborative activities!"
                    )
                }
            }
            .padding()
        }
    }

    // MARK: - Peer Connections View

    private var peerConnectionsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(socialService.peerConnections) { connection in
                    PeerConnectionCard(connection: connection) {
                        Task {
                            await socialService.acceptPeerConnection(connection.id)
                        }
                    } onBlock: {
                        Task {
                            await socialService.blockPeerConnection(connection.id)
                        }
                    }
                }

                if socialService.peerConnections.isEmpty {
                    EmptyStateView(
                        icon: "person.2",
                        title: "No Peer Connections",
                        message: "Join study groups to connect with learning buddies!"
                    )
                }
            }
            .padding()
        }
    }

    // MARK: - Safe Messages View

    private var safeMessagesView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(socialService.safeMessages) { message in
                    SafeMessageCard(message: message) {
                        // Handle message tap
                    }
                }

                if socialService.safeMessages.isEmpty {
                    EmptyStateView(
                        icon: "message",
                        title: "No Messages",
                        message: "Start conversations with your study group members!"
                    )
                }
            }
            .padding()
        }
    }

    // MARK: - Helper Methods

    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "person.3.fill"
        case 1: return "rectangle.3.group.fill"
        case 2: return "person.2.fill"
        case 3: return "message.fill"
        default: return "questionmark"
        }
    }

    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Groups"
        case 1: return "Activities"
        case 2: return "Peers"
        case 3: return "Messages"
        default: return "Unknown"
        }
    }
}

// MARK: - Study Group Card

struct StudyGroupCard: View {
    let group: StudyGroup
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(group.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(group.subject)
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("\(group.memberIds.count)/\(group.maxMembers)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.blue.opacity(0.1))
                            )
                            .foregroundColor(.blue)

                        Text(group.gradeLevel.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Text(group.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Label("Safe Environment", systemImage: "shield.checkered")
                        .font(.caption)
                        .foregroundColor(.green)

                    Spacer()

                    Label("Supervised", systemImage: "eye")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Collaborative Activity Card

struct CollaborativeActivityCard: View {
    let activity: CollaborativeActivity
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(activity.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(activity.subject)
                            .font(.subheadline)
                            .foregroundColor(.purple)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text(activity.type.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.purple.opacity(0.1))
                            )
                            .foregroundColor(.purple)

                        Text("\(activity.participantIds.count)/\(activity.maxParticipants)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Text(activity.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Label("Moderated", systemImage: "checkmark.shield")
                        .font(.caption)
                        .foregroundColor(.green)

                    Spacer()

                    Label("Active", systemImage: "circle.fill")
                        .font(.caption)
                        .foregroundColor(activity.status == .active ? .green : .orange)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.purple.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Peer Connection Card

struct PeerConnectionCard: View {
    let connection: PeerConnection
    let onAccept: () -> Void
    let onBlock: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Study Buddy Request")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(connection.connectionType.rawValue)
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }

                Spacer()

                Text(connection.status.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(statusColor.opacity(0.1))
                    )
                    .foregroundColor(statusColor)
            }

            if connection.status == .pending {
                HStack(spacing: 12) {
                    Button("Accept") {
                        onAccept()
                    }
                    .buttonStyle(.borderedProminent)

                    Button("Block") {
                        onBlock()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var statusColor: Color {
        switch connection.status {
        case .pending: return .orange
        case .active: return .green
        case .blocked: return .red
        case .inactive: return .gray
        }
    }
}

// MARK: - Safe Message Card

struct SafeMessageCard: View {
    let message: SafeMessage
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(alignment: .top, spacing: 12) {
                Image(systemName: "person.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)

                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Study Buddy")
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        Spacer()

                        Text(formatTimestamp(message.timestamp))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Text(message.content)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .lineLimit(3)

                    HStack {
                        if message.moderationStatus == .approved {
                            Label("Approved", systemImage: "checkmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                        }

                        Spacer()

                        if message.readAt == nil {
                            Circle()
                                .fill(.blue)
                                .frame(width: 8, height: 8)
                        }
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func formatTimestamp(_ timestamp: String) -> String {
        // Simplified timestamp formatting
        return "2 min ago"
    }
}

// MARK: - Missing View Placeholders

struct CreateStudyGroupView: View {
    var body: some View {
        Text("Create Study Group - Coming Soon")
            .navigationTitle("Create Group")
    }
}

struct CollaborativeActivityDetailView: View {
    let activity: CollaborativeActivity

    var body: some View {
        Text("Collaborative Activity Detail - Coming Soon")
            .navigationTitle(activity.title)
    }
}

#Preview {
    SocialFeaturesView()
}
