//
//  ParentPortalView.swift
//  SpecialSparkAI
//
//  Phase 3: Parent Portal Interface
//

import SwiftUI
import Charts

struct ParentPortalView: View {
    @StateObject private var parentPortalService = ParentPortalService.shared
    @StateObject private var mockAuthService = MockAuthService.shared
    @State private var selectedTab: ParentPortalTab = .dashboard
    @State private var showingReportDetail = false
    @State private var selectedReport: StudentReport?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Selection
                parentPortalTabs

                // Content
                TabView(selection: $selectedTab) {
                    dashboardView
                        .tag(ParentPortalTab.dashboard)

                    progressView
                        .tag(ParentPortalTab.progress)

                    communicationView
                        .tag(ParentPortalTab.communication)

                    settingsView
                        .tag(ParentPortalTab.settings)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Parent Portal")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadParentData()
            }
        }
        .sheet(isPresented: $showingReportDetail) {
            if let report = selectedReport {
                StudentReportDetailView(report: report)
            }
        }
    }

    // MARK: - Tab Selection
    private var parentPortalTabs: some View {
        HStack(spacing: 0) {
            ForEach(ParentPortalTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.iconName)
                            .font(.title3)

                        Text(tab.title)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .blue : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(.ultraThinMaterial)
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(.gray.opacity(0.3)),
            alignment: .bottom
        )
    }

    // MARK: - Dashboard View
    private var dashboardView: some View {
        ScrollView {
            VStack(spacing: 20) {
                if let dashboard = parentPortalService.parentDashboard {
                    // Weekly Overview
                    weeklyOverviewCard(dashboard.weeklyProgress)

                    // Recent Alerts
                    recentAlertsCard(dashboard.recentAlerts)

                    // Quick Actions
                    quickActionsCard

                    // Subject Progress
                    if let weeklyProgress = dashboard.weeklyProgress {
                        subjectProgressCard(weeklyProgress.subjectBreakdown)
                    }
                } else if parentPortalService.isLoading {
                    ProgressView("Loading dashboard...")
                        .frame(height: 200)
                } else {
                    Text("Unable to load dashboard")
                        .foregroundColor(.secondary)
                }
            }
            .padding()
        }
    }

    // MARK: - Progress View
    private var progressView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Progress Charts
                progressChartsCard

                // Achievement Summary
                achievementSummaryCard

                // Learning Analytics
                learningAnalyticsCard
            }
            .padding()
        }
    }

    // MARK: - Communication View
    private var communicationView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Message Teacher
                messageTeacherCard

                // Communication History
                communicationHistoryCard

                // Schedule Meeting
                scheduleMeetingCard
            }
            .padding()
        }
    }

    // MARK: - Settings View
    private var settingsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Notification Settings
                notificationSettingsCard

                // Privacy Settings
                privacySettingsCard

                // Report Preferences
                reportPreferencesCard
            }
            .padding()
        }
    }

    // MARK: - Dashboard Cards
    private func weeklyOverviewCard(_ progress: WeeklyProgress?) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("This Week's Overview")
                .font(.headline)
                .fontWeight(.semibold)

            if let progress = progress {
                HStack(spacing: 20) {
                    OverviewStatCard(
                        title: "Learning Time",
                        value: "\(progress.totalLearningTime / 60)h \(progress.totalLearningTime % 60)m",
                        icon: "clock.fill",
                        color: .blue
                    )

                    OverviewStatCard(
                        title: "Lessons",
                        value: "\(progress.lessonsCompleted)",
                        icon: "book.fill",
                        color: .green
                    )

                    OverviewStatCard(
                        title: "Achievements",
                        value: "\(progress.achievementsEarned)",
                        icon: "star.fill",
                        color: .orange
                    )
                }

                HStack {
                    Text("Engagement Level")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("\(Int(progress.averageEngagement))%")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }

                ProgressView(value: progress.averageEngagement / 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private func recentAlertsCard(_ alerts: [ParentAlert]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Updates")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    // Show all alerts
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if alerts.isEmpty {
                Text("No recent updates")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical)
            } else {
                ForEach(alerts.prefix(3)) { alert in
                    AlertRow(alert: alert)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var quickActionsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 16) {
                QuickActionButton(
                    title: "Message Teacher",
                    icon: "message.fill",
                    color: .blue
                ) {
                    selectedTab = .communication
                }

                QuickActionButton(
                    title: "View Report",
                    icon: "doc.text.fill",
                    color: .green
                ) {
                    generateReport()
                }

                QuickActionButton(
                    title: "Schedule Meeting",
                    icon: "calendar.badge.plus",
                    color: .orange
                ) {
                    selectedTab = .communication
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private func subjectProgressCard(_ subjects: [SubjectProgressSummary]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Subject Progress")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(subjects) { subject in
                SubjectProgressRow(subject: subject)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    // MARK: - Progress Cards
    private var progressChartsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Learning Progress")
                .font(.headline)
                .fontWeight(.semibold)

            // Mock chart data
            Chart(0..<7, id: \.self) { day in
                BarMark(
                    x: .value("Day", "Day \(day + 1)"),
                    y: .value("Minutes", Int.random(in: 30...90))
                )
                .foregroundStyle(.blue)
            }
            .frame(height: 200)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var achievementSummaryCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Achievement Summary")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 20) {
                AchievementSummaryItem(
                    title: "Total Badges",
                    count: 12,
                    icon: "rosette",
                    color: .purple
                )

                AchievementSummaryItem(
                    title: "This Month",
                    count: 3,
                    icon: "star.fill",
                    color: .orange
                )

                AchievementSummaryItem(
                    title: "Streak",
                    count: 7,
                    icon: "flame.fill",
                    color: .red
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var learningAnalyticsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Learning Insights")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                InsightRow(
                    title: "Peak Learning Time",
                    value: "9:00 AM - 11:00 AM",
                    icon: "clock.fill"
                )

                InsightRow(
                    title: "Favorite Subject",
                    value: "Science",
                    icon: "atom"
                )

                InsightRow(
                    title: "Learning Style",
                    value: "Visual Learner",
                    icon: "eye.fill"
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    // MARK: - Communication Cards
    private var messageTeacherCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Message Teacher")
                .font(.headline)
                .fontWeight(.semibold)

            Button("Compose Message") {
                // Show message composer
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var communicationHistoryCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Communications")
                .font(.headline)
                .fontWeight(.semibold)

            if parentPortalService.communicationHistory.isEmpty {
                Text("No recent communications")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical)
            } else {
                ForEach(parentPortalService.communicationHistory.prefix(3)) { communication in
                    CommunicationRow(communication: communication)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var scheduleMeetingCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Schedule Meeting")
                .font(.headline)
                .fontWeight(.semibold)

            Button("Request Meeting") {
                // Show meeting scheduler
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(.green)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    // MARK: - Settings Cards
    private var notificationSettingsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Notification Settings")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                NotificationToggle(title: "Achievement Notifications", isOn: .constant(true))
                NotificationToggle(title: "Progress Updates", isOn: .constant(true))
                NotificationToggle(title: "Teacher Messages", isOn: .constant(true))
                NotificationToggle(title: "Weekly Reports", isOn: .constant(false))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var privacySettingsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Privacy Settings")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                PrivacyToggle(title: "Share Progress with School", isOn: .constant(true))
                PrivacyToggle(title: "Allow Data Analytics", isOn: .constant(true))
                PrivacyToggle(title: "Third-party Integrations", isOn: .constant(false))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    private var reportPreferencesCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Report Preferences")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                ReportPreferenceRow(title: "Report Frequency", value: "Weekly")
                ReportPreferenceRow(title: "Report Format", value: "Detailed")
                ReportPreferenceRow(title: "Delivery Method", value: "Email")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }

    // MARK: - Actions
    private func loadParentData() {
        guard let _ = mockAuthService.currentStudent else { return }

        Task {
            await parentPortalService.loadParentDashboard(for: UUID()) // Mock parent ID
        }
    }

    private func generateReport() {
        guard let student = mockAuthService.currentStudent else { return }

        Task {
            let report = await parentPortalService.generateStudentReport(for: student.id, period: .weekly)
            await MainActor.run {
                self.selectedReport = report
                self.showingReportDetail = true
            }
        }
    }
}

// MARK: - Parent Portal Tab
enum ParentPortalTab: String, CaseIterable {
    case dashboard = "Dashboard"
    case progress = "Progress"
    case communication = "Communication"
    case settings = "Settings"

    var title: String {
        return self.rawValue
    }

    var iconName: String {
        switch self {
        case .dashboard: return "house.fill"
        case .progress: return "chart.line.uptrend.xyaxis"
        case .communication: return "message.fill"
        case .settings: return "gearshape.fill"
        }
    }
}

// MARK: - Supporting View Components

struct OverviewStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

struct AlertRow: View {
    let alert: ParentAlert

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: alert.type.iconName)
                .font(.title3)
                .foregroundColor(alert.type.color)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(alert.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(alert.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                Text(alert.timestamp)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if !alert.isRead {
                Circle()
                    .fill(.blue)
                    .frame(width: 8, height: 8)
            }
        }
        .padding(.vertical, 4)
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SubjectProgressRow: View {
    let subject: SubjectProgressSummary

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(subject.subject)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Text("\(subject.timeSpent) min")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            ProgressView(value: subject.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))

            HStack {
                Text("\(Int(subject.progress * 100))% Complete")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()
            }
        }
    }
}

struct AchievementSummaryItem: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
    }
}

struct InsightRow: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }

            Spacer()
        }
    }
}

struct CommunicationRow: View {
    let communication: ParentCommunication

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: communication.communicationType.iconName)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(communication.subject)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(communication.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                Text(communication.timestamp.formatted(date: .abbreviated, time: .shortened))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct NotificationToggle: View {
    let title: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)

            Spacer()

            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
    }
}

struct PrivacyToggle: View {
    let title: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)

            Spacer()

            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
    }
}

struct ReportPreferenceRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)

            Spacer()

            Text(value)
                .font(.subheadline)
                .foregroundColor(.blue)
        }
    }
}

// MARK: - Student Report Detail View
struct StudentReportDetailView: View {
    let report: StudentReport
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Report Header
                    VStack(spacing: 8) {
                        Text("Student Report")
                            .font(.title)
                            .fontWeight(.bold)

                        Text(report.period.rawValue)
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("Generated: \(report.generatedAt.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )

                    // Academic Progress
                    if let academicProgress = report.academicProgress {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Academic Progress")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text("Overall Grade: \(academicProgress.overallGrade)")
                                .font(.subheadline)

                            ForEach(academicProgress.subjectGrades.keys.sorted(), id: \.self) { subject in
                                HStack {
                                    Text(subject)
                                    Spacer()
                                    Text(academicProgress.subjectGrades[subject] ?? "N/A")
                                        .fontWeight(.medium)
                                }
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                        )
                    }

                    // Behavioral Insights
                    if let behavioralInsights = report.behavioralInsights {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Behavioral Insights")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text("Engagement Level: \(Int(behavioralInsights.engagementLevel))%")
                            Text("Attention Span: \(behavioralInsights.attentionSpan) minutes")
                            Text("Social Interaction: \(Int(behavioralInsights.socialInteraction))%")
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                        )
                    }

                    // Recommendations
                    if !report.recommendations.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Recommendations")
                                .font(.headline)
                                .fontWeight(.semibold)

                            ForEach(report.recommendations, id: \.self) { recommendation in
                                Text("• \(recommendation)")
                                    .font(.subheadline)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("Report Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ParentPortalView()
}
