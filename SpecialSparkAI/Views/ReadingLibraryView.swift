//
//  ReadingLibraryView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct ReadingLibraryView: View {
    let gradeLevel: GradeLevel
    @StateObject private var lessonDataService = LessonDataService()
    @State private var selectedGenre: BookGenre?
    @State private var selectedBook: Book?
    @State private var showingBookDetail = false
    @State private var searchText = ""
    @Environment(\.dismiss) private var dismiss
    
    private var filteredBooks: [Book] {
        let gradeBooks = lessonDataService.getBooksForGrade(gradeLevel)
        
        if searchText.isEmpty && selectedGenre == nil {
            return gradeBooks
        }
        
        return gradeBooks.filter { book in
            let matchesSearch = searchText.isEmpty || 
                book.title.localizedCaseInsensitiveContains(searchText) ||
                book.author.localizedCaseInsensitiveContains(searchText)
            
            let matchesGenre = selectedGenre == nil || book.genre == selectedGenre
            
            return matchesSearch && matchesGenre
        }
    }
    
    private var recommendedBooks: [Book] {
        return lessonDataService.getRecommendedBooks(for: gradeLevel, limit: 8)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Search and Filter
                searchAndFilterView
                
                // Content
                ScrollView {
                    VStack(spacing: 20) {
                        // Recommended Books
                        if !recommendedBooks.isEmpty {
                            recommendedBooksSection
                        }
                        
                        // All Books
                        allBooksSection
                        
                        // Reading Activities Tracker
                        readingActivitiesSection
                    }
                    .padding()
                }
            }
            .navigationTitle("Reading Library")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingBookDetail) {
            if let book = selectedBook {
                BookDetailView(book: book, gradeLevel: gradeLevel)
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            Text("Grade \(gradeLevel.displayName) Reading Library")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Discover books and track your reading journey")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // Reading stats
            HStack(spacing: 20) {
                ReadingStatView(title: "Available Books", value: "\(lessonDataService.getBooksForGrade(gradeLevel).count)", icon: "books.vertical")
                ReadingStatView(title: "Recommended", value: "\(recommendedBooks.count)", icon: "star.fill")
                ReadingStatView(title: "Reading Level", value: gradeLevel.displayName, icon: "graduationcap")
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    private var searchAndFilterView: some View {
        VStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search books or authors...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            // Genre filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    GenreFilterButton(
                        title: "All",
                        isSelected: selectedGenre == nil
                    ) {
                        selectedGenre = nil
                    }
                    
                    ForEach(BookGenre.allCases, id: \.self) { genre in
                        GenreFilterButton(
                            title: genre.displayName,
                            isSelected: selectedGenre == genre
                        ) {
                            selectedGenre = selectedGenre == genre ? nil : genre
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
    }
    
    private var recommendedBooksSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Recommended for You")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(recommendedBooks) { book in
                        RecommendedBookCard(book: book) {
                            selectedBook = book
                            showingBookDetail = true
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    private var allBooksSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("All Books")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(filteredBooks.count) books")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                ForEach(filteredBooks) { book in
                    BookGridCard(book: book) {
                        selectedBook = book
                        showingBookDetail = true
                    }
                }
            }
        }
    }
    
    private var readingActivitiesSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Reading Activities")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                ReadingActivityCard(
                    title: "Reading Challenge",
                    description: "Read 5 books this month",
                    progress: 60,
                    icon: "target",
                    color: .blue
                )
                
                ReadingActivityCard(
                    title: "Vocabulary Builder",
                    description: "Learn 20 new words",
                    progress: 75,
                    icon: "textformat.abc",
                    color: .green
                )
                
                ReadingActivityCard(
                    title: "Reading Comprehension",
                    description: "Complete 3 comprehension quizzes",
                    progress: 33,
                    icon: "questionmark.circle",
                    color: .purple
                )
            }
        }
    }
}

// MARK: - Supporting Views

struct ReadingStatView: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct GenreFilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? .blue : Color(.systemGray5))
                .cornerRadius(15)
        }
    }
}

struct RecommendedBookCard: View {
    let book: Book
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Book cover
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 100, height: 130)
                    .overlay(
                        VStack {
                            Image(systemName: "star.fill")
                                .foregroundColor(.yellow)
                                .font(.caption)
                            Image(systemName: "book.fill")
                                .foregroundColor(.white)
                                .font(.title2)
                        }
                    )
                
                // Book info
                VStack(spacing: 4) {
                    Text(book.title)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(book.author)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            .frame(width: 120)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct BookGridCard: View {
    let book: Book
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Book cover
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [book.genre.color.opacity(0.3), book.genre.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(height: 120)
                    .overlay(
                        Image(systemName: "book.fill")
                            .foregroundColor(.white)
                            .font(.title2)
                    )
                
                // Book info
                VStack(spacing: 4) {
                    Text(book.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(book.author)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    HStack {
                        Text(book.genre.displayName)
                            .font(.caption2)
                            .foregroundColor(book.genre.color)
                        
                        Spacer()
                        
                        Text("\(book.pageCount) pages")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ReadingActivityCard: View {
    let title: String
    let description: String
    let progress: Int
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                ProgressView(value: Double(progress), total: 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: color))
            }
            
            Text("\(progress)%")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

// Extension for BookGenre colors
extension BookGenre {
    var color: Color {
        switch self {
        case .fiction: return .blue
        case .nonFiction: return .green
        case .biography: return .orange
        case .mystery: return .purple
        case .adventure: return .red
        case .fantasy: return .pink
        case .scienceFiction: return .cyan
        case .historical: return .brown
        case .poetry: return .mint
        case .drama: return .indigo
        case .informational: return .teal
        case .howTo: return .yellow
        case .reference: return .gray
        case .textbook: return .black
        case .pictureBook: return .rainbow
        case .graphicNovel: return .primary
        }
    }
}

extension Color {
    static let rainbow = LinearGradient(
        colors: [.red, .orange, .yellow, .green, .blue, .purple],
        startPoint: .leading,
        endPoint: .trailing
    )
}

#Preview {
    ReadingLibraryView(gradeLevel: .grade3)
}
