//
//  AdaptiveLearningTestView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct AdaptiveLearningTestView: View {
    @StateObject private var adaptiveLearningService = AdaptiveLearningService.shared
    @State private var testResults: [String] = []
    @State private var isRunningTests = false
    @State private var selectedTest = "All Tests"
    
    private let testOptions = [
        "All Tests",
        "Difficulty Adaptation",
        "Personalization",
        "Emotional Tracking",
        "Progress Update",
        "Recommendations"
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 10) {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    Text("Adaptive Learning System")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Test the AI-powered personalization engine")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Test Selection
                VStack(alignment: .leading, spacing: 10) {
                    Text("Select Test")
                        .font(.headline)
                    
                    Picker("Test Type", selection: $selectedTest) {
                        ForEach(testOptions, id: \.self) { option in
                            Text(option).tag(option)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // Run Tests Button
                Button(action: {
                    runSelectedTest()
                }) {
                    HStack {
                        if isRunningTests {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "play.circle.fill")
                        }
                        Text(isRunningTests ? "Running Tests..." : "Run \(selectedTest)")
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(isRunningTests ? Color.gray : Color.blue)
                    .cornerRadius(10)
                }
                .disabled(isRunningTests)
                
                // Test Results
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 10) {
                        if testResults.isEmpty {
                            Text("No tests run yet")
                                .foregroundColor(.secondary)
                                .italic()
                        } else {
                            ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                                TestResultCard(result: result, index: index + 1)
                            }
                        }
                    }
                    .padding()
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("AI Testing")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func runSelectedTest() {
        isRunningTests = true
        testResults.removeAll()
        
        Task {
            switch selectedTest {
            case "All Tests":
                await runAllTests()
            case "Difficulty Adaptation":
                await testDifficultyAdaptation()
            case "Personalization":
                await testPersonalization()
            case "Emotional Tracking":
                await testEmotionalTracking()
            case "Progress Update":
                await testProgressUpdate()
            case "Recommendations":
                await testRecommendations()
            default:
                await runAllTests()
            }
            
            await MainActor.run {
                isRunningTests = false
            }
        }
    }
    
    private func runAllTests() async {
        await testDifficultyAdaptation()
        await testPersonalization()
        await testEmotionalTracking()
        await testProgressUpdate()
        await testRecommendations()
    }
    
    private func testDifficultyAdaptation() async {
        addTestResult("🎯 Testing Difficulty Adaptation...")
        
        let studentId = UUID()
        let subjectId = UUID()
        
        // Test high performance
        let hardDifficulty = await adaptiveLearningService.adaptDifficultyLevel(
            studentId: studentId,
            subjectId: subjectId,
            currentPerformance: 0.9,
            responseTime: 5.0,
            emotionalState: "confident"
        )
        
        addTestResult("✅ High performance → \(hardDifficulty)")
        
        // Test low performance
        let easyDifficulty = await adaptiveLearningService.adaptDifficultyLevel(
            studentId: studentId,
            subjectId: subjectId,
            currentPerformance: 0.3,
            responseTime: 30.0,
            emotionalState: "frustrated"
        )
        
        addTestResult("✅ Low performance → \(easyDifficulty)")
    }
    
    private func testPersonalization() async {
        addTestResult("👤 Testing Personalization...")
        
        let studentProfile = StudentProfile(
            id: UUID(),
            firstName: "Alex",
            lastName: "Test",
            dateOfBirth: "2015-03-15",
            gradeLevel: "Grade 3",
            schoolLevel: "Elementary",
            profileImageURL: nil,
            parentEmail: "<EMAIL>",
            specialNeeds: ["ADHD", "Visual Processing"],
            learningStyle: "visual",
            preferredLanguage: "en",
            timezone: "UTC",
            isActive: true,
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z"
        )
        
        let personalization = await adaptiveLearningService.personalizeTeachingApproach(
            for: studentProfile,
            subject: "Mathematics",
            currentTopic: "Addition"
        )
        
        addTestResult("✅ Teaching Style: \(personalization.teachingStyle)")
        addTestResult("✅ Visual Aids: \(personalization.visualAids.joined(separator: ", "))")
        addTestResult("✅ Support: \(personalization.supportStrategies.joined(separator: ", "))")
    }
    
    private func testEmotionalTracking() async {
        addTestResult("😊 Testing Emotional Tracking...")
        
        let emotionalIndicators = EmotionalIndicators(
            primaryState: "frustrated",
            confidence: 0.3,
            engagement: 0.2,
            frustration: 0.8,
            excitement: 0.1,
            context: "Math problem solving",
            triggers: ["Difficult problem", "Time pressure"]
        )
        
        await adaptiveLearningService.trackEmotionalState(
            studentId: UUID(),
            sessionId: UUID(),
            indicators: emotionalIndicators
        )
        
        addTestResult("✅ Emotional state tracked: \(emotionalIndicators.primaryState)")
        addTestResult("✅ Frustration level: \(Int(emotionalIndicators.frustration * 100))%")
    }
    
    private func testProgressUpdate() async {
        addTestResult("📈 Testing Progress Update...")
        
        let assessmentResult = AssessmentRecord(
            id: UUID(),
            studentId: UUID(),
            sessionId: UUID(),
            questionId: UUID(),
            questionText: "What is 5 + 3?",
            studentAnswer: "8",
            correctAnswer: "8",
            isCorrect: true,
            responseTime: 10.0,
            hintsUsed: 0,
            difficultyLevel: "medium",
            skillTested: "Addition",
            adaptiveAdjustment: nil,
            createdAt: "2024-01-01T00:00:00Z"
        )
        
        await adaptiveLearningService.updateLearningProgress(
            studentId: UUID(),
            subjectId: UUID(),
            skillId: UUID(),
            assessmentResult: assessmentResult
        )
        
        addTestResult("✅ Progress updated for skill: \(assessmentResult.skillTested)")
        addTestResult("✅ Correct answer in \(assessmentResult.responseTime)s")
    }
    
    private func testRecommendations() async {
        addTestResult("💡 Testing Recommendations...")
        
        await adaptiveLearningService.generateAdaptiveRecommendations(for: UUID())
        
        let recommendations = adaptiveLearningService.currentRecommendations
        
        if recommendations.isEmpty {
            addTestResult("✅ Recommendations generated (mock data)")
        } else {
            addTestResult("✅ Generated \(recommendations.count) recommendations")
            for (index, rec) in recommendations.prefix(3).enumerated() {
                addTestResult("  \(index + 1). \(rec.title)")
            }
        }
    }
    
    private func addTestResult(_ result: String) {
        Task { @MainActor in
            testResults.append(result)
        }
    }
}

struct TestResultCard: View {
    let result: String
    let index: Int
    
    var body: some View {
        HStack(alignment: .top, spacing: 10) {
            Text("\(index)")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 20)
            
            Text(result)
                .font(.system(.body, design: .monospaced))
                .foregroundColor(result.contains("✅") ? .green : 
                               result.contains("❌") ? .red : .primary)
            
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
}

struct AdaptiveLearningTestView_Previews: PreviewProvider {
    static var previews: some View {
        AdaptiveLearningTestView()
    }
}
