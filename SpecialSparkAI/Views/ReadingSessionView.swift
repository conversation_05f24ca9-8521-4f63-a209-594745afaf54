//
//  ReadingSessionView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct ReadingSessionView: View {
    let book: Book
    @Binding var currentPage: Int
    @State private var sessionStartTime = Date()
    @State private var readingNotes = ""
    @State private var showingNotes = false
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Session Header
                sessionHeaderView
                
                // Reading Content Area
                readingContentView
                
                // Reading Controls
                readingControlsView
                
                Spacer()
            }
            .padding()
            .navigationTitle("Reading Session")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("End Session") {
                        endReadingSession()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingNotes = true
                    }) {
                        Image(systemName: "note.text")
                    }
                }
            }
        }
        .sheet(isPresented: $showingNotes) {
            ReadingNotesView(notes: $readingNotes, currentPage: currentPage)
        }
    }
    
    private var sessionHeaderView: some View {
        VStack(spacing: 12) {
            Text(book.title)
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("by \(book.author)")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack(spacing: 20) {
                Label("Page \(currentPage)", systemImage: "doc.text")
                Label(timeElapsed, systemImage: "clock")
                Label("\(progressPercentage)%", systemImage: "chart.line.uptrend.xyaxis")
            }
            .font(.caption)
            .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var readingContentView: some View {
        VStack(spacing: 15) {
            Text("Reading Content")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Simulated reading content
            ScrollView {
                VStack(alignment: .leading, spacing: 15) {
                    Text("Page \(currentPage)")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    
                    Text(sampleContent)
                        .font(.body)
                        .lineSpacing(4)
                        .foregroundColor(.primary)
                }
                .padding()
            }
            .frame(maxHeight: 300)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
    }
    
    private var readingControlsView: some View {
        VStack(spacing: 15) {
            // Page navigation
            HStack(spacing: 20) {
                Button(action: {
                    if currentPage > 1 {
                        currentPage -= 1
                    }
                }) {
                    HStack {
                        Image(systemName: "chevron.left")
                        Text("Previous")
                    }
                    .font(.subheadline)
                    .foregroundColor(currentPage > 1 ? .blue : .gray)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
                .disabled(currentPage <= 1)
                
                Spacer()
                
                Text("Page \(currentPage) of \(book.pageCount)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Button(action: {
                    if currentPage < book.pageCount {
                        currentPage += 1
                    }
                }) {
                    HStack {
                        Text("Next")
                        Image(systemName: "chevron.right")
                    }
                    .font(.subheadline)
                    .foregroundColor(currentPage < book.pageCount ? .blue : .gray)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
                .disabled(currentPage >= book.pageCount)
            }
            
            // Quick actions
            HStack(spacing: 15) {
                QuickActionButton(
                    title: "Bookmark",
                    icon: "bookmark",
                    color: .orange
                ) {
                    // Add bookmark
                }
                
                QuickActionButton(
                    title: "Highlight",
                    icon: "highlighter",
                    color: .yellow
                ) {
                    // Add highlight
                }
                
                QuickActionButton(
                    title: "Note",
                    icon: "note.text",
                    color: .blue
                ) {
                    showingNotes = true
                }
                
                QuickActionButton(
                    title: "Dictionary",
                    icon: "book.closed",
                    color: .green
                ) {
                    // Open dictionary
                }
            }
        }
    }
    
    private var timeElapsed: String {
        let elapsed = Date().timeIntervalSince(sessionStartTime)
        let minutes = Int(elapsed) / 60
        let seconds = Int(elapsed) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private var progressPercentage: Int {
        return Int((Double(currentPage) / Double(book.pageCount)) * 100)
    }
    
    private var sampleContent: String {
        // Sample reading content based on the book
        switch book.genre {
        case .fiction:
            return "Once upon a time, in a land far away, there lived a young adventurer who was about to embark on the most incredible journey of their life. The morning sun cast long shadows across the meadow as they prepared for what lay ahead..."
        case .nonFiction:
            return "Did you know that the human brain contains approximately 86 billion neurons? Each neuron can connect to thousands of other neurons, creating a complex network that allows us to think, learn, and remember..."
        case .scienceFiction:
            return "The spaceship hummed quietly as it traveled through the vast emptiness of space. Captain Sarah looked out at the stars and wondered what new worlds they would discover on this mission to the outer reaches of the galaxy..."
        default:
            return "This is an engaging story that will capture your imagination and take you on an amazing adventure. As you read each page, you'll discover new characters, exciting plot twists, and valuable lessons that will stay with you long after you finish the book."
        }
    }
    
    private func endReadingSession() {
        // Save reading progress and session data
        dismiss()
    }
}

// MARK: - Supporting Views

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
}

struct ReadingNotesView: View {
    @Binding var notes: String
    let currentPage: Int
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Reading Notes - Page \(currentPage)")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                TextEditor(text: $notes)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .frame(minHeight: 200)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Notes")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Save notes
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ReadingSessionView(
        book: Book(
            title: "Charlotte's Web",
            author: "E.B. White",
            gradeLevel: .grade3,
            genre: .fiction,
            pageCount: 184
        ),
        currentPage: .constant(25)
    )
}
