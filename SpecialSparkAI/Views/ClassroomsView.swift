//
//  ClassroomsView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct ClassroomsView: View {
    let student: Student?
    @Query private var lessons: [Lesson]
    @State private var selectedSubject: TeacherSubject = .mathematics
    @State private var showingLessonDetail = false
    @State private var selectedLesson: Lesson?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Subject selector
                subjectSelectorView

                // Classrooms content
                ScrollView {
                    VStack(spacing: 20) {
                        // Current class status
                        currentClassStatusView

                        // Available lessons
                        availableLessonsView

                        // Recommended lessons
                        recommendedLessonsView

                        // Progress in current subject
                        subjectProgressView
                    }
                    .padding()
                }
            }
            .navigationTitle("Classrooms")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingLessonDetail) {
            if let lesson = selectedLesson {
                LessonDetailView(lesson: lesson, student: student)
            }
        }
    }

    private var subjectSelectorView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 15) {
                ForEach(TeacherSubject.allCases, id: \.self) { subject in
                    SubjectChip(
                        subject: subject,
                        isSelected: selectedSubject == subject
                    ) {
                        selectedSubject = subject
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 10)
        .background(.ultraThinMaterial)
    }

    private var currentClassStatusView: some View {
        VStack(spacing: 15) {
            HStack {
                Text("Current Class")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                // Live indicator
                HStack(spacing: 5) {
                    Circle()
                        .fill(.red)
                        .frame(width: 8, height: 8)
                        .scaleEffect(1.0)
                        .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: true)

                    Text("LIVE")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                }
            }

            // Current class card
            if let currentClass = getCurrentClass() {
                CurrentClassCard(lesson: currentClass, student: student) {
                    selectedLesson = currentClass
                    showingLessonDetail = true
                }
            } else {
                NoCurrentClassView(selectedSubject: selectedSubject.rawValue)
            }
        }
    }

    private var availableLessonsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Available Lessons")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    // Show all lessons
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            let filteredLessons = lessons.filter { $0.subject.name.contains(selectedSubject.rawValue) }

            if filteredLessons.isEmpty {
                EmptyLessonsView(subject: selectedSubject.rawValue)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(filteredLessons.prefix(3)) { lesson in
                        LessonCard(lesson: lesson, student: student) {
                            selectedLesson = lesson
                            showingLessonDetail = true
                        }
                    }
                }
            }
        }
    }

    private var recommendedLessonsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Recommended for You")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Image(systemName: "sparkles")
                    .foregroundColor(.yellow)
                    .font(.caption)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(getRecommendedLessons()) { lesson in
                        RecommendedLessonCard(lesson: lesson) {
                            selectedLesson = lesson
                            showingLessonDetail = true
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private var subjectProgressView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Your Progress in \(selectedSubject.rawValue)")
                .font(.headline)
                .fontWeight(.semibold)

            SubjectProgressCard(
                subject: selectedSubject.rawValue,
                progress: getSubjectProgress(),
                level: getSubjectLevel(),
                color: subjectColor(for: selectedSubject)
            )
        }
    }

    // Helper methods
    private func getCurrentClass() -> Lesson? {
        // In a real app, this would check for currently active lessons
        return lessons.first { lesson in
            lesson.subject.name.contains(selectedSubject.rawValue)
        }
    }

    private func getRecommendedLessons() -> [Lesson] {
        // In a real app, this would use AI to recommend lessons based on student profile
        return Array(lessons.filter { $0.subject.name.contains(selectedSubject.rawValue) }.prefix(3))
    }

    private func getSubjectProgress() -> Double {
        // Calculate progress based on completed lessons
        return 0.65 // Mock data
    }

    private func getSubjectLevel() -> String {
        guard let student = student else { return "Beginner" }
        return "Grade \(student.gradeLevel)"
    }

    private func subjectColor(for subject: TeacherSubject) -> Color {
        switch subject {
        case .mathematics:
            return .blue
        case .english:
            return .purple
        case .science:
            return .green
        case .socialStudies:
            return .orange
        case .art:
            return .pink
        case .music:
            return .indigo
        case .physicalEducation:
            return .red
        case .computerScience:
            return .teal
        case .foreignLanguage:
            return .mint
        case .specialEducation:
            return .cyan
        case .counseling:
            return .brown
        case .libraryScience:
            return .gray
        }
    }

    // Helper function to convert Subject struct to TeacherSubject enum
    private func convertToTeacherSubject(_ subject: Subject) -> TeacherSubject {
        let name = subject.name.lowercased()
        if name.contains("math") {
            return .mathematics
        } else if name.contains("english") || name.contains("language arts") {
            return .english
        } else if name.contains("science") {
            return .science
        } else if name.contains("social") || name.contains("history") {
            return .socialStudies
        } else if name.contains("art") {
            return .art
        } else if name.contains("music") {
            return .music
        } else if name.contains("physical") || name.contains("pe") {
            return .physicalEducation
        } else if name.contains("computer") {
            return .computerScience
        } else if name.contains("foreign") || name.contains("spanish") || name.contains("french") {
            return .foreignLanguage
        } else if name.contains("special") {
            return .specialEducation
        } else if name.contains("counsel") {
            return .counseling
        } else if name.contains("library") {
            return .libraryScience
        } else {
            return .mathematics // Default fallback
        }
    }
}

struct SubjectChip: View {
    let subject: TeacherSubject
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: subjectIcon(for: subject))
                    .font(.caption)

                Text(subjectShortName(for: subject))
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? subjectColor(for: subject) : .clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(subjectColor(for: subject), lineWidth: 1)
                    )
            )
            .foregroundColor(isSelected ? .white : subjectColor(for: subject))
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func subjectIcon(for subject: TeacherSubject) -> String {
        switch subject {
        case .mathematics:
            return "function"
        case .english:
            return "book"
        case .science:
            return "flask"
        case .socialStudies:
            return "globe"
        case .art:
            return "paintbrush"
        case .music:
            return "music.note"
        case .physicalEducation:
            return "figure.run"
        case .computerScience:
            return "laptopcomputer"
        case .foreignLanguage:
            return "globe.badge.chevron.backward"
        case .specialEducation:
            return "heart"
        case .counseling:
            return "leaf"
        case .libraryScience:
            return "books.vertical"
        }
    }

    private func subjectShortName(for subject: TeacherSubject) -> String {
        switch subject {
        case .mathematics:
            return "Math"
        case .english:
            return "English"
        case .science:
            return "Science"
        case .socialStudies:
            return "Social Studies"
        case .art:
            return "Art"
        case .music:
            return "Music"
        case .physicalEducation:
            return "PE"
        case .computerScience:
            return "Computer"
        case .foreignLanguage:
            return "Language"
        case .specialEducation:
            return "Special Ed"
        case .counseling:
            return "Counseling"
        case .libraryScience:
            return "Library"
        }
    }

    private func subjectColor(for subject: TeacherSubject) -> Color {
        switch subject {
        case .mathematics:
            return .blue
        case .english:
            return .purple
        case .science:
            return .green
        case .socialStudies:
            return .orange
        case .art:
            return .pink
        case .music:
            return .indigo
        case .physicalEducation:
            return .red
        case .computerScience:
            return .teal
        case .foreignLanguage:
            return .mint
        case .specialEducation:
            return .cyan
        case .counseling:
            return .brown
        case .libraryScience:
            return .gray
        }
    }
}

struct CurrentClassCard: View {
    let lesson: Lesson
    let student: Student?
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(lesson.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)

                        Text("with AI Teacher")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        HStack {
                            Label("\(lesson.duration) min", systemImage: "clock")
                            Label("Grade \(lesson.gradeLevel)", systemImage: "graduationcap")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }

                    Spacer()

                    VStack {
                        Image(systemName: "play.circle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.green)

                        Text("Join Class")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                }

                // Progress bar
                VStack(spacing: 8) {
                    HStack {
                        Text("Lesson Progress")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text("45% Complete")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }

                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(.ultraThinMaterial)
                                .frame(height: 6)

                            RoundedRectangle(cornerRadius: 4)
                                .fill(.blue)
                                .frame(width: geometry.size.width * 0.45, height: 6)
                        }
                    }
                    .frame(height: 6)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.green.opacity(0.3), lineWidth: 2)
                    )
                    .shadow(radius: 5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct NoCurrentClassView: View {
    let selectedSubject: String

    var body: some View {
        VStack(spacing: 15) {
            Image(systemName: "clock")
                .font(.largeTitle)
                .foregroundColor(.secondary)

            Text("No Active Class")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Start a new \(selectedSubject) lesson below")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct LessonCard: View {
    let lesson: Lesson
    let student: Student?
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                // Subject icon
                Image(systemName: subjectIcon(for: convertToTeacherSubject(lesson.subject)))
                    .font(.title2)
                    .foregroundColor(subjectColor(for: convertToTeacherSubject(lesson.subject)))
                    .frame(width: 40)

                // Content
                VStack(alignment: .leading, spacing: 5) {
                    Text(lesson.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    if !lesson.details.isEmpty {
                        Text(lesson.details)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }

                    HStack {
                        Label("\(lesson.duration) min", systemImage: "clock")
                        Label("Grade \(lesson.gradeLevel)", systemImage: "graduationcap")
                        if !lesson.objectives.isEmpty {
                            Label("\(lesson.objectives.count) objectives", systemImage: "target")
                        }
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }

                Spacer()

                // Start button
                Image(systemName: "chevron.right.circle.fill")
                    .font(.title3)
                    .foregroundColor(.blue)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func subjectIcon(for subject: TeacherSubject) -> String {
        switch subject {
        case .mathematics:
            return "function"
        case .english:
            return "book"
        case .science:
            return "flask"
        case .socialStudies:
            return "globe"
        case .art:
            return "paintbrush"
        case .music:
            return "music.note"
        case .physicalEducation:
            return "figure.run"
        case .computerScience:
            return "laptopcomputer"
        case .foreignLanguage:
            return "globe.badge.chevron.backward"
        case .specialEducation:
            return "heart"
        case .counseling:
            return "leaf"
        case .libraryScience:
            return "books.vertical"
        }
    }

    private func subjectColor(for subject: TeacherSubject) -> Color {
        switch subject {
        case .mathematics:
            return .blue
        case .english:
            return .purple
        case .science:
            return .green
        case .socialStudies:
            return .orange
        case .art:
            return .pink
        case .music:
            return .indigo
        case .physicalEducation:
            return .red
        case .computerScience:
            return .teal
        case .foreignLanguage:
            return .mint
        case .specialEducation:
            return .cyan
        case .counseling:
            return .brown
        case .libraryScience:
            return .gray
        }
    }

    // Helper function to convert Subject struct to TeacherSubject enum
    private func convertToTeacherSubject(_ subject: Subject) -> TeacherSubject {
        let name = subject.name.lowercased()
        if name.contains("math") {
            return .mathematics
        } else if name.contains("english") || name.contains("language arts") {
            return .english
        } else if name.contains("science") {
            return .science
        } else if name.contains("social") || name.contains("history") {
            return .socialStudies
        } else if name.contains("art") {
            return .art
        } else if name.contains("music") {
            return .music
        } else if name.contains("physical") || name.contains("pe") {
            return .physicalEducation
        } else if name.contains("computer") {
            return .computerScience
        } else if name.contains("foreign") || name.contains("spanish") || name.contains("french") {
            return .foreignLanguage
        } else if name.contains("special") {
            return .specialEducation
        } else if name.contains("counsel") {
            return .counseling
        } else if name.contains("library") {
            return .libraryScience
        } else {
            return .mathematics // Default fallback
        }
    }
}

struct RecommendedLessonCard: View {
    let lesson: Lesson
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Recommendation badge
                HStack {
                    Spacer()
                    HStack(spacing: 4) {
                        Image(systemName: "sparkles")
                            .font(.caption2)
                        Text("Recommended")
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.yellow)
                }

                // Content
                VStack(spacing: 8) {
                    Text(lesson.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    Text("\(lesson.duration) minutes")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("Perfect for your level")
                        .font(.caption2)
                        .foregroundColor(.blue)
                        .fontWeight(.medium)
                }

                // Start button
                Button("Start Lesson") {
                    action()
                }
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 6)
                .background(.blue)
                .cornerRadius(12)
            }
            .frame(width: 140)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct EmptyLessonsView: View {
    let subject: String

    var body: some View {
        VStack(spacing: 15) {
            Image(systemName: "book.closed")
                .font(.largeTitle)
                .foregroundColor(.secondary)

            Text("No \(subject) Lessons Yet")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("New lessons are being created by our AI teachers")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Request Lesson") {
                // Request new lesson
            }
            .font(.subheadline)
            .foregroundColor(.blue)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct LessonDetailView: View {
    let lesson: Lesson
    let student: Student?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Lesson Detail View")
                        .font(.title)

                    Text("Detailed view of \(lesson.title) would go here")
                        .font(.body)
                        .foregroundColor(.secondary)

                    // Add detailed lesson interface
                }
                .padding()
            }
            .navigationTitle(lesson.title)
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

#Preview {
    ClassroomsView(student: nil)
}
