//
//  ScienceLabsView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct ScienceLabsView: View {
    let gradeLevel: GradeLevel
    @StateObject private var lessonDataService = LessonDataService()
    @State private var selectedDomain: ScienceDomain?
    @State private var selectedExperiment: ScienceExperiment?
    @State private var showingExperimentDetail = false
    @Environment(\.dismiss) private var dismiss
    
    private var filteredExperiments: [ScienceExperiment] {
        let gradeExperiments = lessonDataService.getExperimentsForGrade(gradeLevel)
        
        if let domain = selectedDomain {
            return gradeExperiments.filter { $0.scienceDomain == domain }
        }
        
        return gradeExperiments
    }
    
    private var availableDomains: [ScienceDomain] {
        let experiments = lessonDataService.getExperimentsForGrade(gradeLevel)
        return Array(Set(experiments.map { $0.scienceDomain })).sorted { $0.displayName < $1.displayName }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Domain Filter
                domainFilterView
                
                // Experiments List
                ScrollView {
                    VStack(spacing: 20) {
                        // Featured Experiments
                        featuredExperimentsSection
                        
                        // All Experiments
                        allExperimentsSection
                        
                        // Safety Guidelines
                        safetyGuidelinesSection
                    }
                    .padding()
                }
            }
            .navigationTitle("Science Labs")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingExperimentDetail) {
            if let experiment = selectedExperiment {
                ExperimentDetailView(experiment: experiment, gradeLevel: gradeLevel)
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            Text("Grade \(gradeLevel.displayName) Science Experiments")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Discover, explore, and learn through hands-on experiments")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // Science stats
            HStack(spacing: 20) {
                ScienceStatView(title: "Experiments", value: "\(filteredExperiments.count)", icon: "flask")
                ScienceStatView(title: "Domains", value: "\(availableDomains.count)", icon: "atom")
                ScienceStatView(title: "Safety Level", value: "Monitored", icon: "shield.checkered")
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    private var domainFilterView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                DomainFilterButton(
                    title: "All",
                    isSelected: selectedDomain == nil
                ) {
                    selectedDomain = nil
                }
                
                ForEach(availableDomains, id: \.self) { domain in
                    DomainFilterButton(
                        title: domain.displayName,
                        isSelected: selectedDomain == domain
                    ) {
                        selectedDomain = selectedDomain == domain ? nil : domain
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
    }
    
    private var featuredExperimentsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Featured Experiments")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(filteredExperiments.prefix(3)) { experiment in
                        FeaturedExperimentCard(experiment: experiment) {
                            selectedExperiment = experiment
                            showingExperimentDetail = true
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    private var allExperimentsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("All Experiments")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(filteredExperiments.count) experiments")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            LazyVStack(spacing: 12) {
                ForEach(filteredExperiments) { experiment in
                    ExperimentCard(experiment: experiment) {
                        selectedExperiment = experiment
                        showingExperimentDetail = true
                    }
                }
            }
        }
    }
    
    private var safetyGuidelinesSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Safety Guidelines")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                SafetyGuidelineCard(
                    title: "Adult Supervision",
                    description: "Always have an adult present during experiments",
                    icon: "person.2.fill",
                    color: .red
                )
                
                SafetyGuidelineCard(
                    title: "Read Instructions",
                    description: "Carefully read all instructions before starting",
                    icon: "doc.text.magnifyingglass",
                    color: .blue
                )
                
                SafetyGuidelineCard(
                    title: "Safety Equipment",
                    description: "Use appropriate safety equipment when needed",
                    icon: "shield.fill",
                    color: .green
                )
                
                SafetyGuidelineCard(
                    title: "Clean Up",
                    description: "Always clean up properly after experiments",
                    icon: "sparkles",
                    color: .purple
                )
            }
        }
    }
}

// MARK: - Supporting Views

struct ScienceStatView: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.green)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct DomainFilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? .green : Color(.systemGray5))
                .cornerRadius(15)
        }
    }
}

struct FeaturedExperimentCard: View {
    let experiment: ScienceExperiment
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Experiment icon
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [experiment.scienceDomain.color.opacity(0.3), experiment.scienceDomain.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 100, height: 80)
                    .overlay(
                        VStack {
                            Image(systemName: experiment.scienceDomain.icon)
                                .foregroundColor(.white)
                                .font(.title2)
                            
                            Text(experiment.scienceDomain.displayName)
                                .font(.caption2)
                                .foregroundColor(.white)
                                .fontWeight(.medium)
                        }
                    )
                
                // Experiment info
                VStack(spacing: 4) {
                    Text(experiment.title)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    HStack {
                        Label("\(experiment.duration) min", systemImage: "clock")
                        Label(experiment.safetyLevel.displayName, systemImage: "shield")
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }
            }
            .frame(width: 120)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ExperimentCard: View {
    let experiment: ScienceExperiment
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                // Experiment icon
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [experiment.scienceDomain.color.opacity(0.3), experiment.scienceDomain.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: experiment.scienceDomain.icon)
                            .foregroundColor(.white)
                            .font(.title3)
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(experiment.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(experiment.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack {
                        Label(experiment.scienceDomain.displayName, systemImage: "atom")
                        Label("\(experiment.duration) min", systemImage: "clock")
                        Label(experiment.difficulty.displayName, systemImage: "chart.bar")
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Safety indicator
                VStack {
                    Circle()
                        .fill(experiment.safetyLevel.color)
                        .frame(width: 12, height: 12)
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SafetyGuidelineCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 1)
        )
    }
}

// Extension for ScienceDomain colors and icons
extension ScienceDomain {
    var color: Color {
        switch self {
        case .physics: return .blue
        case .chemistry: return .green
        case .biology: return .mint
        case .earthScience: return .brown
        case .astronomy: return .purple
        case .environmental: return .teal
        case .engineering: return .orange
        case .technology: return .gray
        case .mathematics: return .red
        case .interdisciplinary: return .pink
        }
    }
    
    var icon: String {
        switch self {
        case .physics: return "atom"
        case .chemistry: return "flask"
        case .biology: return "leaf"
        case .earthScience: return "globe"
        case .astronomy: return "moon.stars"
        case .environmental: return "tree"
        case .engineering: return "gearshape"
        case .technology: return "laptopcomputer"
        case .mathematics: return "function"
        case .interdisciplinary: return "network"
        }
    }
}

#Preview {
    ScienceLabsView(gradeLevel: .grade5)
}
