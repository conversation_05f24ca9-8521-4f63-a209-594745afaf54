//
//  ArtProjectDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct ArtProjectDetailView: View {
    let project: ArtProject
    let gradeLevel: GradeLevel
    @State private var selectedTab: ArtProjectTab = .overview
    @State private var showingAIChat = false
    @Environment(\.dismiss) private var dismiss

    enum ArtProjectTab: String, CaseIterable {
        case overview = "Overview"
        case materials = "Materials"
        case steps = "Steps"
        case gallery = "Gallery"

        var icon: String {
            switch self {
            case .overview: return "info.circle.fill"
            case .materials: return "list.bullet.rectangle"
            case .steps: return "list.number"
            case .gallery: return "photo.on.rectangle"
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Project Header
                projectHeaderView

                // Tab Selection
                tabSelectionView

                // Content Area
                TabView(selection: $selectedTab) {
                    overviewView
                        .tag(ArtProjectTab.overview)

                    materialsView
                        .tag(ArtProjectTab.materials)

                    stepsView
                        .tag(ArtProjectTab.steps)

                    galleryView
                        .tag(ArtProjectTab.gallery)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(project.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingAIChat = true
                    }) {
                        Image(systemName: "message.circle.fill")
                            .foregroundColor(.pink)
                    }
                }
            }
        }
        .sheet(isPresented: $showingAIChat) {
            AITeacherChatView(subject: "Art Teacher")
        }
    }

    private var projectHeaderView: some View {
        VStack(spacing: 15) {
            // Project icon and art form
            HStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(
                        colors: [project.artForm.color.opacity(0.3), project.artForm.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: project.artForm.icon)
                            .foregroundColor(.white)
                            .font(.title2)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(project.artForm.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(project.artForm.color)

                    Text(project.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                Spacer()
            }

            // Project metadata
            HStack(spacing: 20) {
                ArtProjectMetadataView(title: "Duration", value: "\(project.duration) min", icon: "clock")
                ArtProjectMetadataView(title: "Difficulty", value: project.difficulty.displayName, icon: "chart.bar")
                ArtProjectMetadataView(title: "Grade", value: project.gradeLevel.displayName, icon: "graduationcap")
            }
        }
        .padding()
    }

    private var tabSelectionView: some View {
        HStack(spacing: 0) {
            ForEach(ArtProjectTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.title3)

                        Text(tab.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .pink : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
            }
        }
        .background(Color(.systemGray6))
        .overlay(
            Rectangle()
                .fill(.pink)
                .frame(height: 2)
                .offset(x: tabOffset, y: 0)
                .animation(.easeInOut(duration: 0.3), value: selectedTab),
            alignment: .bottom
        )
    }

    private var tabOffset: CGFloat {
        let tabWidth = UIScreen.main.bounds.width / CGFloat(ArtProjectTab.allCases.count)
        let index = ArtProjectTab.allCases.firstIndex(of: selectedTab) ?? 0
        return (CGFloat(index) * tabWidth) - (UIScreen.main.bounds.width / 2) + (tabWidth / 2)
    }

    private var overviewView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Project objectives
                ArtSectionCard(title: "Learning Objectives", icon: "target") {
                    VStack(alignment: .leading, spacing: 8) {
                        if project.objectives.isEmpty {
                            ArtObjectiveItem(text: "Develop \(project.artForm.displayName.lowercased()) skills")
                            ArtObjectiveItem(text: "Express creativity and imagination")
                            ArtObjectiveItem(text: "Learn about color, form, and composition")
                            ArtObjectiveItem(text: "Build confidence in artistic expression")
                        } else {
                            ForEach(project.objectives, id: \.self) { objective in
                                ArtObjectiveItem(text: objective)
                            }
                        }
                    }
                }

                // Techniques used
                ArtSectionCard(title: "Techniques You'll Learn", icon: "paintbrush") {
                    VStack(alignment: .leading, spacing: 8) {
                        if project.techniques.isEmpty {
                            ArtTechniqueItem(name: "Basic \(project.artForm.displayName)", description: "Fundamental techniques")
                            ArtTechniqueItem(name: "Color Theory", description: "Understanding colors")
                            ArtTechniqueItem(name: "Composition", description: "Arranging elements")
                        } else {
                            ForEach(project.techniques) { technique in
                                ArtTechniqueItem(name: technique.name, description: technique.description)
                            }
                        }
                    }
                }

                // Inspiration
                ArtSectionCard(title: "Artistic Inspiration", icon: "star") {
                    VStack(alignment: .leading, spacing: 8) {
                        if project.inspirations.isEmpty {
                            ArtInspirationItem(title: "Famous Artists", description: "Study works by master artists")
                            ArtInspirationItem(title: "Nature", description: "Find inspiration in the natural world")
                            ArtInspirationItem(title: "Everyday Objects", description: "See art in common things")
                        } else {
                            ForEach(project.inspirations) { inspiration in
                                ArtInspirationItem(title: inspiration.title, description: inspiration.description)
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }

    private var materialsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Required materials
                ArtSectionCard(title: "Required Materials", icon: "list.bullet.rectangle") {
                    VStack(spacing: 8) {
                        if project.materials.isEmpty {
                            ArtMaterialItem(name: "Paper or Canvas", quantity: "1 sheet", isOptional: false)
                            ArtMaterialItem(name: "Pencils", quantity: "Set", isOptional: false)
                            ArtMaterialItem(name: "Eraser", quantity: "1", isOptional: false)
                            ArtMaterialItem(name: "Colors/Paints", quantity: "Basic set", isOptional: false)
                            ArtMaterialItem(name: "Brushes", quantity: "Various sizes", isOptional: true)
                        } else {
                            ForEach(project.materials) { material in
                                ArtMaterialItem(
                                    name: material.name,
                                    quantity: material.quantity,
                                    isOptional: material.isOptional
                                )
                            }
                        }
                    }
                }

                // Workspace setup
                ArtSectionCard(title: "Workspace Setup", icon: "square.grid.3x3") {
                    VStack(alignment: .leading, spacing: 8) {
                        WorkspaceSetupItem(text: "Find a well-lit area to work")
                        WorkspaceSetupItem(text: "Cover your work surface with newspaper")
                        WorkspaceSetupItem(text: "Have water and paper towels nearby")
                        WorkspaceSetupItem(text: "Organize your materials within reach")
                        WorkspaceSetupItem(text: "Wear old clothes or an apron")
                    }
                }

                // Safety tips
                ArtSectionCard(title: "Safety Tips", icon: "shield") {
                    VStack(alignment: .leading, spacing: 8) {
                        SafetyTipItem(text: "Always use art materials as intended")
                        SafetyTipItem(text: "Keep materials away from your face")
                        SafetyTipItem(text: "Wash hands after using art supplies")
                        SafetyTipItem(text: "Ask for help with sharp tools")
                        SafetyTipItem(text: "Clean up spills immediately")
                    }
                }
            }
            .padding()
        }
    }

    private var stepsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                ArtSectionCard(title: "Step-by-Step Instructions", icon: "list.number") {
                    VStack(spacing: 12) {
                        if project.steps.isEmpty {
                            ArtProjectStep(number: 1, title: "Prepare", instruction: "Set up your workspace and gather materials")
                            ArtProjectStep(number: 2, title: "Sketch", instruction: "Create a light sketch of your design")
                            ArtProjectStep(number: 3, title: "Create", instruction: "Begin working on your art project")
                            ArtProjectStep(number: 4, title: "Refine", instruction: "Add details and make improvements")
                            ArtProjectStep(number: 5, title: "Finish", instruction: "Complete your artwork and clean up")
                        } else {
                            ForEach(project.steps) { step in
                                ArtProjectStep(
                                    number: step.stepNumber,
                                    title: step.title,
                                    instruction: step.instruction
                                )
                            }
                        }
                    }
                }

                // Tips for success
                ArtSectionCard(title: "Tips for Success", icon: "lightbulb") {
                    VStack(alignment: .leading, spacing: 8) {
                        SuccessTipItem(text: "Take your time - art is not a race")
                        SuccessTipItem(text: "Don't worry about making mistakes")
                        SuccessTipItem(text: "Experiment with different techniques")
                        SuccessTipItem(text: "Step back and look at your work often")
                        SuccessTipItem(text: "Have fun and express yourself!")
                    }
                }
            }
            .padding()
        }
    }

    private var galleryView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Example artworks
                ArtSectionCard(title: "Example Artworks", icon: "photo.on.rectangle") {
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 15) {
                        ForEach(0..<4) { index in
                            ExampleArtworkCard(
                                title: "Example \(index + 1)",
                                artist: "Student Artist",
                                artForm: project.artForm
                            )
                        }
                    }
                }

                // Share your work
                ArtSectionCard(title: "Share Your Artwork", icon: "square.and.arrow.up") {
                    VStack(spacing: 12) {
                        Text("When you complete your project, you can:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        VStack(spacing: 8) {
                            ShareOptionItem(text: "Take a photo to save in your portfolio")
                            ShareOptionItem(text: "Show your family and friends")
                            ShareOptionItem(text: "Display it in the virtual gallery")
                            ShareOptionItem(text: "Get feedback from your art teacher")
                        }
                    }
                }
            }
            .padding()
        }
    }
}

// MARK: - Supporting Views

struct ArtProjectMetadataView: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.pink)

            Text(value)
                .font(.caption)
                .fontWeight(.bold)

            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct ArtSectionCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content

    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.pink)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct ArtObjectiveItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.pink)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct ArtTechniqueItem: View {
    let name: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "paintbrush.fill")
                .foregroundColor(.blue)
                .font(.caption)

            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(.caption)
                    .fontWeight(.medium)

                Text(description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct ArtInspirationItem: View {
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "star.fill")
                .foregroundColor(.yellow)
                .font(.caption)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)

                Text(description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct ArtMaterialItem: View {
    let name: String
    let quantity: String
    let isOptional: Bool

    var body: some View {
        HStack {
            Image(systemName: isOptional ? "circle" : "circle.fill")
                .foregroundColor(isOptional ? .gray : .pink)
                .font(.caption)

            Text(name)
                .font(.subheadline)
                .foregroundColor(isOptional ? .secondary : .primary)

            Spacer()

            Text(quantity)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(Color(.systemGray6))
                .cornerRadius(4)

            if isOptional {
                Text("Optional")
                    .font(.caption2)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(4)
            }
        }
    }
}

struct WorkspaceSetupItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "checkmark.square.fill")
                .foregroundColor(.green)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct SafetyTipItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "shield.fill")
                .foregroundColor(.orange)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct ArtProjectStep: View {
    let number: Int
    let title: String
    let instruction: String

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(number)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 30, height: 30)
                .background(.pink)
                .clipShape(Circle())

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(instruction)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

struct SuccessTipItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "lightbulb.fill")
                .foregroundColor(.yellow)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

struct ExampleArtworkCard: View {
    let title: String
    let artist: String
    let artForm: ArtForm

    var body: some View {
        VStack(spacing: 8) {
            // Artwork preview
            RoundedRectangle(cornerRadius: 8)
                .fill(LinearGradient(
                    colors: [artForm.color.opacity(0.4), artForm.color.opacity(0.7)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(height: 80)
                .overlay(
                    Image(systemName: artForm.icon)
                        .foregroundColor(.white)
                        .font(.title3)
                )

            // Artwork info
            VStack(spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .lineLimit(1)

                Text("by \(artist)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
        }
    }
}

struct ShareOptionItem: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "arrow.up.circle.fill")
                .foregroundColor(.blue)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

#Preview {
    ArtProjectDetailView(
        project: ArtProject(
            title: "Self-Portrait Drawing",
            description: "Create a detailed self-portrait using pencils",
            artForm: .drawing,
            gradeLevel: .grade3
        ),
        gradeLevel: .grade3
    )
}
