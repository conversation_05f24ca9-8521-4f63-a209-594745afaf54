//
//  SimpleAITeachersView.swift
//  SpecialSparkAI
//
//  Simplified AI Teachers View with Conversation Integration
//

import SwiftUI

struct SimpleAITeachersView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @State private var selectedTeacher: AITeacherPersonality?
    @State private var showingConversation = false
    @State private var searchText = ""

    // AI Teacher Personalities
    @State private var aiTeachers: [AITeacherPersonality] = AITeacherPersonality.createDefaultTeachers()

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView

                // Search Bar
                searchBar

                // Teachers Grid
                if mockAuthService.isAuthenticated {
                    teachersGrid
                } else {
                    authPrompt
                }
            }
            .navigationTitle("AI Teachers")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingConversation) {
            if let teacher = selectedTeacher,
               let student = mockAuthService.currentStudent {
                NavigationView {
                    ConversationView(student: student, teacher: teacher)
                        .navigationBarTitleDisplayMode(.inline)
                        .toolbar {
                            ToolbarItem(placement: .navigationBarLeading) {
                                Button("Done") {
                                    showingConversation = false
                                }
                            }
                        }
                }
            }
        }
    }

    // MARK: - Header View
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    if let student = mockAuthService.currentStudent {
                        Text("Hello, \(student.firstName)!")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Choose your AI teacher")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text("AI Teachers")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Sign in to start learning")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Teacher Count
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(filteredTeachers.count)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("Teachers")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }

    // MARK: - Search Bar
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search teachers...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button("Clear") {
                    searchText = ""
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
        .padding(.bottom)
    }

    // MARK: - Teachers Grid
    private var teachersGrid: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(filteredTeachers) { teacher in
                    TeacherCard(teacher: teacher) {
                        selectedTeacher = teacher
                        showingConversation = true
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Auth Prompt
    private var authPrompt: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "person.circle")
                .font(.system(size: 80))
                .foregroundColor(.blue.opacity(0.3))

            VStack(spacing: 8) {
                Text("Welcome to SpecialSpark AI")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Sign in to start learning with your AI teachers")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Spacer()
        }
        .padding()
    }

    // MARK: - Computed Properties
    private var filteredTeachers: [AITeacherPersonality] {
        if searchText.isEmpty {
            return aiTeachers
        } else {
            return aiTeachers.filter { teacher in
                teacher.name.localizedCaseInsensitiveContains(searchText) ||
                teacher.subject.localizedCaseInsensitiveContains(searchText) ||
                teacher.traits.joined(separator: " ").localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

// MARK: - Teacher Card
struct TeacherCard: View {
    let teacher: AITeacherPersonality
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Avatar
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color(hex: teacher.backgroundColor), Color(hex: teacher.backgroundColor).opacity(0.7)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: teacher.avatarImageName)
                            .font(.title2)
                            .foregroundColor(.white)
                    )

                VStack(spacing: 4) {
                    Text(teacher.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)

                    Text(teacher.subject)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    Text(teacher.gradeRange)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color(.systemGray5))
                        .clipShape(Capsule())
                }

                // Traits
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 4) {
                        ForEach(teacher.traits.prefix(2), id: \.self) { trait in
                            Text(trait)
                                .font(.caption2)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color(hex: teacher.backgroundColor).opacity(0.2))
                                .foregroundColor(Color(hex: teacher.backgroundColor))
                                .clipShape(Capsule())
                        }
                    }
                }

                // Start Chat Button
                HStack(spacing: 4) {
                    Image(systemName: "message.fill")
                        .font(.caption)
                    Text("Start Chat")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(hex: teacher.backgroundColor))
                .clipShape(Capsule())
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



#Preview {
    SimpleAITeachersView()
}
