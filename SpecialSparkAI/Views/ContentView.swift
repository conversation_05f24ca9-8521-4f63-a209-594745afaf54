//
//  ContentView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @StateObject private var crewAIService = CrewAIService.shared

    var body: some View {
        if !mockAuthService.isAuthenticated {
            StudentProfileView()
        } else {
            // Use MainSchoolView which contains the proper TabView navigation
            MainSchoolView()
        }
    }
    .task {
        // Initialize CrewAI service when app starts
        await crewAIService.initialize()
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Student.self, inMemory: true)
}
