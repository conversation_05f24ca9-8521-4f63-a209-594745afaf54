//
//  ContentView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @StateObject private var mockAuthService = MockAuthService.shared

    var body: some View {
        if !mockAuthService.isAuthenticated {
            StudentProfileView()
        } else {
            // Use MainSchoolView which contains the proper TabView navigation
            MainSchoolView()
        }
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Student.self, inMemory: true)
}
