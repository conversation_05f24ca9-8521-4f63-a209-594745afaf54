//
//  AssessmentInterfaceView.swift
//  SpecialSparkAI
//
//  Phase 4: Enhanced Assessment Interface
//

import SwiftUI

struct AssessmentInterfaceView: View {
    @StateObject private var assessmentEngine = AssessmentEngineService.shared
    @StateObject private var mockAuthService = MockAuthService.shared

    @State private var selectedTab = 0
    @State private var showingCreateAssessment = false
    @State private var showingAssessmentDetail = false
    @State private var selectedAssessment: Assessment?
    @State private var showingResults = false
    @State private var selectedResult: AssessmentSessionResult?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView

                // Tab Selection
                tabSelector

                // Content
                TabView(selection: $selectedTab) {
                    // Available Assessments
                    availableAssessmentsView
                        .tag(0)

                    // Active Assessment
                    activeAssessmentView
                        .tag(1)

                    // Results & Analytics
                    resultsView
                        .tag(2)

                    // Create Assessment
                    createAssessmentView
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Assessment Center")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingCreateAssessment = true }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
        }
        .sheet(isPresented: $showingCreateAssessment) {
            CreateAssessmentView()
        }
        .sheet(isPresented: $showingAssessmentDetail) {
            if let assessment = selectedAssessment {
                AssessmentDetailView(assessment: assessment)
            }
        }
        .sheet(isPresented: $showingResults) {
            if let result = selectedResult {
                AssessmentResultDetailView(result: result)
            }
        }
    }

    // MARK: - Header View

    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Assessment Center")
                        .font(.title2)
                        .fontWeight(.bold)

                    if let student = mockAuthService.currentStudent {
                        Text("Welcome, \(student.firstName)!")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Quick Stats
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(assessmentEngine.availableAssessments.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("Available")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Progress Indicator
            if assessmentEngine.isAssessmentActive {
                assessmentProgressIndicator
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    // MARK: - Tab Selector

    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(0..<4) { index in
                Button(action: { selectedTab = index }) {
                    VStack(spacing: 4) {
                        Image(systemName: tabIcon(for: index))
                            .font(.title3)

                        Text(tabTitle(for: index))
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == index ? .blue : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
        .padding(.top, 8)
    }

    // MARK: - Available Assessments View

    private var availableAssessmentsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(assessmentEngine.availableAssessments) { assessment in
                    AssessmentCard(assessment: assessment) {
                        selectedAssessment = assessment
                        showingAssessmentDetail = true
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Active Assessment View

    private var activeAssessmentView: some View {
        Group {
            if assessmentEngine.isAssessmentActive,
               let assessment = assessmentEngine.currentAssessment,
               let session = assessmentEngine.currentSession {
                ActiveAssessmentView(assessment: assessment, session: session)
            } else {
                EmptyStateView(
                    icon: "doc.text",
                    title: "No Active Assessment",
                    message: "Start an assessment from the Available tab to begin."
                )
            }
        }
    }

    // MARK: - Results View

    private var resultsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(assessmentEngine.assessmentResults) { result in
                    AssessmentResultCard(result: result) {
                        selectedResult = result
                        showingResults = true
                    }
                }

                if assessmentEngine.assessmentResults.isEmpty {
                    EmptyStateView(
                        icon: "chart.bar",
                        title: "No Results Yet",
                        message: "Complete assessments to see your results and analytics."
                    )
                }
            }
            .padding()
        }
    }

    // MARK: - Create Assessment View

    private var createAssessmentView: some View {
        CreateAssessmentFormView()
    }

    // MARK: - Assessment Progress Indicator

    private var assessmentProgressIndicator: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Assessment in Progress")
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                if let session = assessmentEngine.currentSession {
                    Text("\(session.responses.count)/\(assessmentEngine.currentAssessment?.questions.count ?? 0)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                }
            }

            ProgressView(value: progressValue)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.blue.opacity(0.1))
        )
    }

    // MARK: - Helper Methods

    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "list.bullet.clipboard"
        case 1: return "play.circle"
        case 2: return "chart.bar.fill"
        case 3: return "plus.square"
        default: return "questionmark"
        }
    }

    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Available"
        case 1: return "Active"
        case 2: return "Results"
        case 3: return "Create"
        default: return "Unknown"
        }
    }

    private var progressValue: Double {
        guard let session = assessmentEngine.currentSession,
              let assessment = assessmentEngine.currentAssessment else { return 0.0 }

        let completed = Double(session.responses.count)
        let total = Double(assessment.questions.count)

        return total > 0 ? completed / total : 0.0
    }
}

// MARK: - Assessment Card

struct AssessmentCard: View {
    let assessment: Assessment
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(assessment.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(assessment.subject)
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text(assessment.type.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.blue.opacity(0.1))
                            )
                            .foregroundColor(.blue)

                        Text("\(assessment.estimatedDuration) min")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Text(assessment.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Label(assessment.gradeLevel, systemImage: "graduationcap")

                    Spacer()

                    Label(assessment.difficulty.rawValue, systemImage: "speedometer")

                    Spacer()

                    Label("\(assessment.questions.count) questions", systemImage: "questionmark.circle")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Empty State View

struct EmptyStateView: View {
    let icon: String
    let title: String
    let message: String

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 48))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(40)
    }
}

// MARK: - Missing View Placeholders

struct CreateAssessmentView: View {
    var body: some View {
        Text("Create Assessment View - Coming Soon")
            .navigationTitle("Create Assessment")
    }
}

struct AssessmentDetailView: View {
    let assessment: Assessment

    var body: some View {
        Text("Assessment Detail View - Coming Soon")
            .navigationTitle(assessment.title)
    }
}

struct AssessmentResultDetailView: View {
    let result: AssessmentSessionResult

    var body: some View {
        Text("Assessment Result Detail View - Coming Soon")
            .navigationTitle("Results")
    }
}

struct CreateAssessmentFormView: View {
    var body: some View {
        Text("Create Assessment Form - Coming Soon")
    }
}

struct AssessmentResultCard: View {
    let result: AssessmentSessionResult
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Assessment Result")
                    .font(.headline)

                Text("Score: \(result.score.percentage, specifier: "%.1f")%")
                    .font(.subheadline)
                    .foregroundColor(.blue)

                Text("Completed: \(result.completedAt)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AssessmentInterfaceView()
}
