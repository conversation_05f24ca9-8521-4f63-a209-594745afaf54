-- SpecialSparkAI Database Schema
-- Complete K-12 Virtual School with Grade-Based AI Teachers

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- <PERSON>reate custom types
CREATE TYPE grade_level AS ENUM (
  'K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'
);

CREATE TYPE school_level AS ENUM (
  'elementary', 'middle', 'high'
);

CREATE TYPE subject_category AS ENUM (
  'core', 'language', 'arts', 'stem', 'social_studies', 'physical_education', 
  'special_needs', 'ap', 'elective', 'life_skills'
);

CREATE TYPE special_needs_type AS ENUM (
  'autism', 'adhd', 'dyslexia', 'speech_delay', 'learning_disability', 
  'emotional_behavioral', 'intellectual_disability', 'physical_disability', 'none'
);

-- Students table
CREATE TABLE students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  grade_level grade_level NOT NULL,
  school_level school_level NOT NULL,
  date_of_birth DATE,
  special_needs special_needs_type[] DEFAULT ARRAY['none'],
  learning_preferences JSONB DEFAULT '{}',
  parent_email VARCHAR(255),
  parent_phone VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subjects table - Complete K-12 curriculum
CREATE TABLE subjects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL,
  category subject_category NOT NULL,
  school_levels school_level[] NOT NULL,
  grade_levels grade_level[] NOT NULL,
  description TEXT,
  is_ap BOOLEAN DEFAULT FALSE,
  prerequisites VARCHAR(100)[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Teachers/Agents table
CREATE TABLE ai_teachers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  agent_type VARCHAR(50) NOT NULL,
  specialization VARCHAR(100) NOT NULL,
  subject_id UUID REFERENCES subjects(id),
  grade_levels grade_level[] NOT NULL,
  school_levels school_level[] NOT NULL,
  personality_traits JSONB DEFAULT '{}',
  capabilities TEXT[],
  communication_style VARCHAR(50) DEFAULT 'adaptive',
  special_needs_adaptations special_needs_type[],
  avatar_url VARCHAR(255),
  bio TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student-Teacher assignments
CREATE TABLE student_teacher_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  teacher_id UUID REFERENCES ai_teachers(id) ON DELETE CASCADE,
  subject_id UUID REFERENCES subjects(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  UNIQUE(student_id, teacher_id, subject_id)
);

-- Learning sessions
CREATE TABLE learning_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  teacher_id UUID REFERENCES ai_teachers(id),
  subject_id UUID REFERENCES subjects(id),
  session_type VARCHAR(50) NOT NULL,
  duration_minutes INTEGER,
  objectives TEXT[],
  content JSONB,
  progress_score DECIMAL(3,2),
  emotional_state VARCHAR(50),
  adaptations_used TEXT[],
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT
);

-- Insert sample subjects for complete K-12 curriculum
INSERT INTO subjects (name, code, category, school_levels, grade_levels, description, is_ap) VALUES
-- Elementary Core Subjects
('Reading & Language Arts', 'ELA_ELEM', 'core', ARRAY['elementary'], ARRAY['K','1','2','3','4','5'], 'Foundational reading, writing, and language skills', false),
('Mathematics', 'MATH_ELEM', 'core', ARRAY['elementary'], ARRAY['K','1','2','3','4','5'], 'Basic math concepts and problem solving', false),
('Science', 'SCI_ELEM', 'stem', ARRAY['elementary'], ARRAY['K','1','2','3','4','5'], 'Introduction to scientific concepts and inquiry', false),
('Social Studies', 'SS_ELEM', 'social_studies', ARRAY['elementary'], ARRAY['K','1','2','3','4','5'], 'Community, geography, and basic history', false),

-- Middle School Core Subjects
('English Language Arts', 'ELA_MID', 'core', ARRAY['middle'], ARRAY['6','7','8'], 'Advanced reading, writing, and literature', false),
('Pre-Algebra', 'PREALG', 'core', ARRAY['middle'], ARRAY['6','7'], 'Introduction to algebraic concepts', false),
('Algebra I', 'ALG1', 'core', ARRAY['middle','high'], ARRAY['8','9'], 'Linear equations and functions', false),
('Life Science', 'LIFESCI', 'stem', ARRAY['middle'], ARRAY['6','7'], 'Biology basics and life processes', false),
('Physical Science', 'PHYSCI', 'stem', ARRAY['middle'], ARRAY['8'], 'Introduction to chemistry and physics', false),
('World History', 'WHIST', 'social_studies', ARRAY['middle'], ARRAY['6','7'], 'Ancient and medieval civilizations', false),
('US History', 'USHIST_MID', 'social_studies', ARRAY['middle'], ARRAY['8'], 'American history through Civil War', false),

-- High School Core Subjects
('English 9', 'ENG9', 'core', ARRAY['high'], ARRAY['9'], 'Literature and composition', false),
('English 10', 'ENG10', 'core', ARRAY['high'], ARRAY['10'], 'World literature and writing', false),
('English 11', 'ENG11', 'core', ARRAY['high'], ARRAY['11'], 'American literature', false),
('English 12', 'ENG12', 'core', ARRAY['high'], ARRAY['12'], 'British literature and college prep', false),
('Geometry', 'GEOM', 'core', ARRAY['high'], ARRAY['9','10'], 'Geometric proofs and spatial reasoning', false),
('Algebra II', 'ALG2', 'core', ARRAY['high'], ARRAY['10','11'], 'Advanced algebraic concepts', false),
('Pre-Calculus', 'PRECALC', 'core', ARRAY['high'], ARRAY['11','12'], 'Trigonometry and advanced functions', false),
('Biology', 'BIO', 'stem', ARRAY['high'], ARRAY['9','10'], 'Cellular biology and genetics', false),
('Chemistry', 'CHEM', 'stem', ARRAY['high'], ARRAY['10','11'], 'Chemical reactions and bonding', false),
('Physics', 'PHYS', 'stem', ARRAY['high'], ARRAY['11','12'], 'Mechanics and electromagnetic theory', false),
('World History', 'WHIST_HIGH', 'social_studies', ARRAY['high'], ARRAY['9'], 'Global civilizations and cultures', false),
('US History', 'USHIST_HIGH', 'social_studies', ARRAY['high'], ARRAY['10','11'], 'American history and government', false),
('Government', 'GOV', 'social_studies', ARRAY['high'], ARRAY['12'], 'Civics and political systems', false),

-- AP Subjects
('AP English Literature', 'AP_ENGLIT', 'core', ARRAY['high'], ARRAY['11','12'], 'College-level literature analysis', true),
('AP English Language', 'AP_ENGLANG', 'core', ARRAY['high'], ARRAY['11','12'], 'College-level composition and rhetoric', true),
('AP Calculus AB', 'AP_CALCAB', 'stem', ARRAY['high'], ARRAY['11','12'], 'Differential and integral calculus', true),
('AP Calculus BC', 'AP_CALCBC', 'stem', ARRAY['high'], ARRAY['12'], 'Advanced calculus concepts', true),
('AP Biology', 'AP_BIO', 'stem', ARRAY['high'], ARRAY['11','12'], 'College-level biology', true),
('AP Chemistry', 'AP_CHEM', 'stem', ARRAY['high'], ARRAY['11','12'], 'College-level chemistry', true),
('AP Physics 1', 'AP_PHYS1', 'stem', ARRAY['high'], ARRAY['11','12'], 'Algebra-based physics', true),
('AP US History', 'AP_USHIST', 'social_studies', ARRAY['high'], ARRAY['10','11'], 'College-level American history', true),
('AP World History', 'AP_WHIST', 'social_studies', ARRAY['high'], ARRAY['10'], 'College-level world history', true),
('AP Psychology', 'AP_PSYCH', 'social_studies', ARRAY['high'], ARRAY['11','12'], 'Introduction to psychological science', true),

-- Language Subjects
('Spanish I', 'SPAN1', 'language', ARRAY['middle','high'], ARRAY['6','7','8','9'], 'Beginning Spanish language', false),
('Spanish II', 'SPAN2', 'language', ARRAY['middle','high'], ARRAY['7','8','9','10'], 'Intermediate Spanish language', false),
('Spanish III', 'SPAN3', 'language', ARRAY['high'], ARRAY['10','11'], 'Advanced Spanish language', false),
('French I', 'FREN1', 'language', ARRAY['middle','high'], ARRAY['6','7','8','9'], 'Beginning French language', false),
('French II', 'FREN2', 'language', ARRAY['middle','high'], ARRAY['7','8','9','10'], 'Intermediate French language', false),

-- Arts and Electives
('Art', 'ART', 'arts', ARRAY['elementary','middle','high'], ARRAY['K','1','2','3','4','5','6','7','8','9','10','11','12'], 'Visual arts and creativity', false),
('Music', 'MUSIC', 'arts', ARRAY['elementary','middle','high'], ARRAY['K','1','2','3','4','5','6','7','8','9','10','11','12'], 'Music theory and performance', false),
('Physical Education', 'PE', 'physical_education', ARRAY['elementary','middle','high'], ARRAY['K','1','2','3','4','5','6','7','8','9','10','11','12'], 'Physical fitness and health', false),

-- Special Needs Support
('Speech Therapy', 'SPEECH', 'special_needs', ARRAY['elementary','middle','high'], ARRAY['K','1','2','3','4','5','6','7','8','9','10','11','12'], 'Communication skills development', false),
('Occupational Therapy', 'OT', 'special_needs', ARRAY['elementary','middle','high'], ARRAY['K','1','2','3','4','5','6','7','8','9','10','11','12'], 'Daily living skills and motor development', false),
('Social Skills', 'SOCIAL', 'special_needs', ARRAY['elementary','middle','high'], ARRAY['K','1','2','3','4','5','6','7','8','9','10','11','12'], 'Social interaction and communication', false);

-- Enable Row Level Security
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_teacher_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for Row Level Security
CREATE POLICY "Students can view their own data" ON students FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Students can update their own data" ON students FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Everyone can view subjects" ON subjects FOR SELECT TO authenticated USING (true);
CREATE POLICY "Everyone can view ai_teachers" ON ai_teachers FOR SELECT TO authenticated USING (true);

CREATE POLICY "Students can view their assignments" ON student_teacher_assignments FOR SELECT USING (
  student_id IN (SELECT id FROM students WHERE user_id = auth.uid())
);

CREATE POLICY "Students can view their sessions" ON learning_sessions FOR SELECT USING (
  student_id IN (SELECT id FROM students WHERE user_id = auth.uid())
);
