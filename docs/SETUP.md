# SpecialSparkAI - Development Setup Guide

## Prerequisites

### Required Software
- **Xcode 15.0+** with iOS 17.0+ SDK
- **macOS Ventura 13.0+** or later
- **Git** for version control
- **Node.js 18+** (for Supabase CLI)

### Accounts & Services
- **Apple Developer Account** (for device testing)
- **Supabase Account** (for backend services)
- **Google Cloud Account** (for Gemini API)

## Project Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd SpecialSparkAI
```

### 2. Xcode Configuration
1. Open `SpecialSparkAI.xcodeproj` in Xcode
2. Select your development team in project settings
3. Update bundle identifier if needed
4. Ensure iOS 17.0+ deployment target

### 3. Dependencies
The project uses Swift Package Manager for dependencies:

**Current Dependencies:**
- **Supabase Swift**: Backend integration
- **SwiftData**: Local data persistence
- **Combine**: Reactive programming

**To Add Dependencies:**
1. File → Add Package Dependencies
2. Enter package URL
3. Select version requirements
4. Add to target

## Environment Configuration

### 1. Supabase Setup
Create `Config.swift` with your Supabase credentials:

```swift
struct Config {
    static let supabaseURL = "YOUR_SUPABASE_URL"
    static let supabaseAnonKey = "YOUR_SUPABASE_ANON_KEY"
}
```

### 2. Gemini API Setup
Add Gemini API configuration to `Config.swift`:

```swift
struct Config {
    // ... existing config
    static let geminiAPIKey = "YOUR_GEMINI_API_KEY"
    static let geminiModel = "gemini-1.5-flash-002"
}
```

### 3. Database Schema
Run the following SQL in your Supabase dashboard:

```sql
-- Students table
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    grade_level TEXT NOT NULL,
    special_needs JSONB DEFAULT '[]',
    sensory_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Teachers table
CREATE TABLE ai_teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    personality JSONB NOT NULL,
    grade_specialization TEXT[] NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subjects table
CREATE TABLE subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    grade_levels TEXT[] NOT NULL,
    description TEXT,
    icon_name TEXT,
    color_hex TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Sessions table
CREATE TABLE learning_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id),
    teacher_id UUID REFERENCES ai_teachers(id),
    subject_id UUID REFERENCES subjects(id),
    session_data JSONB NOT NULL,
    duration_minutes INTEGER,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Achievements table
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id),
    title TEXT NOT NULL,
    description TEXT,
    badge_icon TEXT,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Project Structure

### Core Files
```
SpecialSparkAI/
├── App/
│   ├── SpecialSparkAIApp.swift     # App entry point
│   └── Config.swift                # Configuration constants
├── Views/
│   ├── ContentView.swift           # Root navigation
│   ├── MainSchoolView.swift        # Main TabView
│   ├── StudentProfileView.swift    # Onboarding
│   ├── AITeachersView.swift        # AI agents
│   ├── ClassroomsView.swift        # Learning spaces
│   ├── VirtualCampusView.swift     # Virtual environment
│   ├── AchievementsView.swift      # Progress tracking
│   ├── SettingsView.swift          # App settings
│   ├── SensorySettingsView.swift   # Accessibility
│   └── AdaptiveLearningTestView.swift # Assessment
├── Models/
│   ├── Student.swift               # Student entity
│   ├── AITeacher.swift            # AI teacher model
│   ├── Subject.swift              # Subject definitions
│   ├── Achievement.swift          # Achievement model
│   └── SensorySettings.swift      # Accessibility model
├── Services/
│   ├── MockAuthService.swift      # Auth simulation
│   ├── SupabaseService.swift      # Backend integration
│   ├── GeminiService.swift        # AI integration
│   └── AdaptiveLearningService.swift # Learning algorithms
└── Resources/
    ├── Assets.xcassets            # Images and colors
    └── Info.plist                 # App configuration
```

## Development Workflow

### 1. Authentication Flow
Currently using mock authentication for development:
- `MockAuthService.shared.isAuthenticated` controls app state
- Student profile creation simulated locally
- Replace with Supabase Auth for production

### 2. AI Integration
AI teachers use agentic architecture:
- **LangGraph**: For complex conversation flows
- **CrewAI**: For multi-agent coordination
- **Gemini Flash 2.0**: For natural language processing

### 3. Data Flow
```
SwiftUI Views → ViewModels → Services → Supabase/Local Storage
```

### 4. Testing Strategy
- **Unit Tests**: Business logic and services
- **UI Tests**: Critical user flows
- **Integration Tests**: Supabase connectivity
- **AI Tests**: Agent response validation

## Common Development Tasks

### Adding New Views
1. Create SwiftUI view in appropriate folder
2. Add navigation in `MainSchoolView.swift`
3. Update TabView if needed
4. Add preview for development

### Adding New Models
1. Create Swift model conforming to `Codable`
2. Add SwiftData persistence if needed
3. Update Supabase schema
4. Create service methods

### Integrating AI Features
1. Define agent personality in `AITeacher` model
2. Implement conversation flow in service
3. Add Gemini API integration
4. Test with mock responses

### Accessibility Implementation
1. Add sensory setting options
2. Implement UI adaptations
3. Test with accessibility tools
4. Validate with special needs requirements

## Debugging & Troubleshooting

### Common Issues
1. **Build Errors**: Check Swift version and dependencies
2. **Supabase Connection**: Verify URL and API keys
3. **AI Integration**: Check Gemini API quotas
4. **Navigation Issues**: Verify TabView structure

### Debug Tools
- **Xcode Debugger**: Breakpoints and variable inspection
- **SwiftUI Preview**: Real-time UI development
- **Supabase Dashboard**: Database monitoring
- **Console Logs**: Runtime debugging

## Deployment

### Development
- Use Xcode simulator for initial testing
- Test on physical device for performance
- Use TestFlight for beta distribution

### Production
- Configure production Supabase environment
- Set up proper API key management
- Enable App Store Connect integration
- Implement analytics and crash reporting

## Resources

### Documentation
- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)
- [Supabase Swift Guide](https://supabase.com/docs/reference/swift)
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [Gemini API Reference](https://ai.google.dev/docs)

### Support
- Check [ARCHITECTURE.md](ARCHITECTURE.md) for system design
- Review [API.md](API.md) for backend integration
- See [CONTRIBUTING.md](CONTRIBUTING.md) for development guidelines
