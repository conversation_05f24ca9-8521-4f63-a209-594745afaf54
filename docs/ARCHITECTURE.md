# SpecialSparkAI - Virtual School App Architecture

## Overview
SpecialSparkAI is a virtual school iOS app designed to become the #1 ranked school in the USA, with a special focus on empowering special needs children and maximizing parent satisfaction. The app features AI agent teachers built with agentic frameworks like CrewAI and LangGraph.

## Core Vision
- **Virtual-First Design**: Emphasizes virtual nature rather than mimicking physical school elements
- **AI-Powered Education**: AI agent teachers with specialized personalities for personalized learning
- **Special Needs Focus**: Comprehensive sensory settings and adaptive learning systems
- **Parent Satisfaction**: Transparent progress tracking and communication tools

## Technology Stack

### Frontend (iOS)
- **Framework**: SwiftUI
- **Data Persistence**: SwiftData
- **Architecture Pattern**: MVVM with Combine
- **Minimum iOS Version**: iOS 17.0+

### Backend
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with mock implementation
- **Real-time Features**: Supabase Realtime subscriptions

### AI/ML
- **AI Framework**: LangGraph and CrewAI for agentic architecture
- **LLM Model**: Gemini Flash 2.0
- **AI Teachers**: Specialized agent personalities with subject expertise

## App Architecture

### Navigation Structure
The app uses a single TabView navigation pattern to avoid duplication:

```
ContentView (Root)
├── StudentProfileView (Onboarding/Auth)
└── MainSchoolView (Main App)
    ├── Home (Dashboard)
    ├── Campus (Virtual Environment)
    ├── Classes (Classrooms)
    ├── Teachers (AI Agents)
    ├── Progress (Achievements)
    └── Settings (Configuration)
```

### Key Components

#### 1. Authentication Flow
- **StudentProfileView**: Handles onboarding and profile creation
- **MockAuthService**: Simulates authentication for development
- **Grade Level Selection**: K-12 grade filtering during onboarding

#### 2. Main Dashboard
- **SchoolDashboardView**: Central hub with personalized greeting
- **Quick Stats**: Lessons, achievements, grade level
- **Today's Schedule**: Dynamic lesson scheduling
- **Quick Actions**: Navigation shortcuts

#### 3. AI Teachers System
- **AITeachersView**: Gallery of AI agent teachers
- **Agentic Architecture**: LangGraph/CrewAI implementation
- **Specialized Personalities**: Subject-specific AI characteristics
- **Gemini Integration**: Real-time AI conversations

#### 4. Adaptive Learning
- **AdaptiveLearningTestView**: Assessment and adaptation engine
- **Student Profiles**: Personalized learning paths
- **Progress Tracking**: Comprehensive analytics

#### 5. Special Needs Support
- **SensorySettingsView**: Comprehensive accessibility controls
- **Adaptive UI**: Dynamic interface adjustments
- **Parent Controls**: Detailed configuration options

## Data Models

### Core Entities
```swift
// Student profile with grade-based filtering
Student {
    id: UUID
    firstName: String
    lastName: String
    gradeLevel: GradeLevel (K-12)
    specialNeeds: [SpecialNeed]
    sensorySettings: SensorySettings
}

// AI Teacher agents
AITeacher {
    id: UUID
    name: String
    subject: Subject
    personality: TeacherPersonality
    gradeSpecialization: [GradeLevel]
}

// Learning progress tracking
LearningProgress {
    studentId: UUID
    subject: Subject
    level: Int
    achievements: [Achievement]
    adaptiveData: AdaptiveMetrics
}
```

### Supabase Schema
- **students**: Student profiles and preferences
- **ai_teachers**: AI agent configurations
- **subjects**: Grade-filtered subject catalog
- **learning_sessions**: Session tracking and analytics
- **achievements**: Gamification system
- **sensory_settings**: Accessibility configurations

## File Organization

### Views Structure
```
Views/
├── ContentView.swift           # Root navigation
├── MainSchoolView.swift        # Main TabView container
├── StudentProfileView.swift    # Onboarding/Auth
├── AITeachersView.swift        # AI agents gallery
├── ClassroomsView.swift        # Learning environments
├── VirtualCampusView.swift     # Virtual spaces
├── AchievementsView.swift      # Progress & gamification
├── SettingsView.swift          # App configuration
├── SensorySettingsView.swift   # Accessibility controls
└── AdaptiveLearningTestView.swift # Assessment engine
```

### Models Structure
```
Models/
├── Student.swift              # Core student entity
├── AITeacher.swift           # AI agent definitions
├── Subject.swift             # Grade-filtered subjects
├── Achievement.swift         # Gamification models
├── SensorySettings.swift     # Accessibility models
└── LearningProgress.swift    # Analytics models
```

### Services Structure
```
Services/
├── MockAuthService.swift     # Authentication simulation
├── SupabaseService.swift     # Backend integration
├── GeminiService.swift       # AI model integration
├── AdaptiveLearningService.swift # Learning algorithms
└── SensoryService.swift      # Accessibility management
```

## Key Features Implementation

### 1. Grade-Based Filtering
- Subjects filtered by student's grade level during onboarding
- AI teachers specialized for specific grade ranges
- Age-appropriate content and complexity

### 2. AI Agent Teachers
- LangGraph/CrewAI framework integration
- Gemini Flash 2.0 model for conversations
- Specialized personalities per subject
- Real-time adaptive responses

### 3. Sensory Settings
- Comprehensive accessibility controls
- Visual, auditory, and tactile adjustments
- Parent-managed configuration
- Real-time UI adaptation

### 4. Adaptive Learning
- Continuous assessment and adjustment
- Personalized learning paths
- Progress analytics and reporting
- Parent dashboard integration

### 5. Gamification
- Achievement system with badges
- Progress rewards and milestones
- Safe peer interaction features
- Motivational feedback loops

## Development Guidelines

### Code Standards
- Follow SwiftUI best practices
- Use MVVM architecture pattern
- Implement proper error handling
- Maintain accessibility compliance

### Testing Strategy
- Unit tests for business logic
- UI tests for critical user flows
- Integration tests for Supabase
- AI agent response validation

### Performance Considerations
- Lazy loading for large datasets
- Efficient image caching
- Background processing for AI calls
- Memory management for real-time features

## Future Enhancements
- Parent portal web dashboard
- Advanced assessment engine
- Enhanced peer collaboration
- Multi-language support
- Offline learning capabilities

## Getting Started
See [SETUP.md](SETUP.md) for detailed setup instructions and [API.md](API.md) for backend integration details.
